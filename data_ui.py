import json, os, tempfile, sys, traceback, time, logging
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QTextEdit, QStackedWidget, QMessageBox,
    QGroupBox, QFormLayout, QCheckBox, QGridLayout, QProgressBar, QDialog,
    QTableWidget, QTableWidgetItem, QRadioButton, QComboBox, QScrollArea,
    QHeaderView, QProgressDialog, QStatusBar, QListView, QSpacerItem,
    QMenu, QToolButton
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QKeySequence, QShortcut, QAction, QTextCharFormat, QUndoStack, QUndoCommand, QCloseEvent, QColor, QTextCursor
from typing import List, Dict, Set, Tuple
import requests
from requests.adapters import HTTPAdapter
from urllib3.util import Retry

from raw_data_core import (
    run_core_logic, BASE64_CREDENTIALS, auto_map_columns, get_headers_from_sheet,
    TEMPLATE_SPREADSHEET_ID, TEMPLATE_SHEET_TITLE, copy_sheet_template, extend_rows_with_formatting,
    process_copied_data, batch_update_sheet  # Added the new functions here
)
from gsheet_manager import GoogleSheetManager

logging.getLogger('googleapiclient.discovery_cache').setLevel(logging.ERROR)

# ---------------------- Loading Dialog ----------------------
class LoadingDialog(QDialog):
    def __init__(self, parent=None, total_sheets: int = 0):
        super().__init__(parent)
        self.setWindowTitle("Loading...")
        self.setModal(True)
        self.setFixedSize(300, 100)
        layout = QVBoxLayout()

        self.info_label = QLabel("Loading sheets...")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.info_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, total_sheets)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)

        self.setLayout(layout)

    def update_progress(self, value: int):
        self.progress_bar.setValue(value)
        self.info_label.setText(f"Loading sheet {value}/{self.progress_bar.maximum()}...")
        QApplication.processEvents()

# ---------------------- Progress Dialog ----------------------
class ProgressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Processing...")
        self.setModal(True)
        self.setFixedSize(650, 350)  # Tăng kích thước dialog
        layout = QVBoxLayout()

        self.info_label = QLabel("Starting process...")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.info_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)
        layout.addWidget(self.progress_bar)

        # Thêm log area để hiển thị thông tin chi tiết
        label = QLabel("Chi tiết xử lý:")
        layout.addWidget(label)

        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setFixedHeight(200)  # Đủ cao để hiển thị nhiều dòng
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def update_info(self, message):
        self.info_label.setText(message)
        QApplication.processEvents()

    def update_error(self, error_message):
        # Hiển thị lỗi với màu đỏ
        self.log_area.setTextColor(QColor(255, 0, 0))
        self.log_area.append(f"❌ LỖI: {error_message}")
        self.log_area.setTextColor(QColor(0, 0, 0))
        self.log_area.ensureCursorVisible()
        QApplication.processEvents()

    def update_success(self, message):
        # Thành công thì không hiển thị gì theo yêu cầu
        pass

    def update_batch_info(self, batch_info):
        # Chỉ hiển thị thông tin batch đang xử lý
        self.update_info(f"Đang xử lý batch {batch_info.get('batch_index', '?')}/{batch_info.get('total_batches', '?')} - {batch_info.get('rows', 0)} dòng")
        QApplication.processEvents()

    def clear_log(self):
        self.log_area.clear()
        QApplication.processEvents()

# ---------------------- Sheet Loader Thread ----------------------
class SheetLoader(QThread):
    loaded = pyqtSignal(list)
    error = pyqtSignal(str)

    def __init__(self, gs_service, source_id):
        super().__init__()
        self.gs_service = gs_service
        self.source_id = source_id

    def run(self):
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Thiết lập timeout dài hơn cho request
                request = self.gs_service.spreadsheets().get(
                    spreadsheetId=self.source_id,
                    fields="sheets(properties(title,sheetId))"
                )
                # Thêm timeout dài hơn cho request
                request.http.timeout = 60  # Tăng timeout lên 60 giây

                result = request.execute()
                sheets = result.get("sheets", [])
                self.loaded.emit(sheets)
                return  # Thoát vòng lặp nếu thành công
            except Exception as e:
                retry_count += 1
                error_msg = str(e)

                if retry_count < max_retries:
                    # Log và thử lại sau 2 giây
                    print(f"Lỗi kết nối, thử lại lần {retry_count}/{max_retries}: {error_msg}")
                    time.sleep(2)  # Đợi 2 giây trước khi thử lại
                else:
                    # Đã hết số lần thử, báo lỗi
                    self.error.emit(f"Error loading sheets after {max_retries} attempts: {error_msg}")
                    return

# ---------------------- Sheet Data Processing Function ----------------------
def process_sheet_data(gs_service, source_id, sheet_title, append_mode, timeout=120):
    start_row = 1
    row_count = 0
    error_msg = ""
    red_row_found = False

    # Cấu hình retry cho requests
    session = requests.Session()
    retry_strategy = Retry(
        total=5,  # Tăng số lần retry
        backoff_factor=2,  # Tăng thời gian giữa các lần retry
        status_forcelist=[429, 500, 502, 503, 504],  # Thêm 429 (too many requests)
        allowed_methods=["GET", "POST"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)

    try:
        # Thiết lập timeout dài hơn cho các API requests
        def execute_with_retry(request, max_attempts=3):
            for attempt in range(max_attempts):
                try:
                    request.http.timeout = 60  # Tăng timeout lên 60 giây
                    return request.execute()
                except Exception as e:
                    if attempt < max_attempts - 1:
                        print(f"API request failed, retrying ({attempt+1}/{max_attempts}): {str(e)}")
                        time.sleep(2 * (attempt + 1))  # Tăng dần thời gian đợi
                    else:
                        raise e

        # Đếm số dòng (row_count) dựa trên các cột E, F, G, H
        # Thay đổi: Kiểm tra nhiều cột thay vì chỉ cột G
        columns_to_check = ["E", "F", "G", "H"]
        last_data_row = 0

        # Lưu danh sách các dòng có dữ liệu trong các cột E, F, G, H để kiểm tra sau này
        rows_with_data = set()

        for column in columns_to_check:
            range_name = f"'{sheet_title}'!{column}3:{column}"
            request = gs_service.spreadsheets().values().get(
                spreadsheetId=source_id,
                range=range_name,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            result = execute_with_retry(request)
            values = result.get('values', [])

            # Tìm dòng cuối cùng có dữ liệu trong cột này
            current_last_row = 0
            for idx, row in enumerate(values):
                if row and row[0] not in [None, ""]:
                    current_last_row = idx + 3  # +3 vì bắt đầu từ dòng 3
                    rows_with_data.add(idx + 3)  # Thêm dòng có dữ liệu vào tập hợp

            # Cập nhật last_data_row nếu tìm thấy dòng xa hơn
            last_data_row = max(last_data_row, current_last_row)

        # Tính số dòng dữ liệu từ dòng cuối cùng đã tìm thấy - CHỈ SỬ DỤNG ĐỂ GHI LOG
        row_count = len(rows_with_data)  # Đếm chính xác số dòng có dữ liệu
        print(f"Đã quét các cột E, F, G, H: last_data_row={last_data_row}, row_count={row_count}, số dòng có dữ liệu={len(rows_with_data)}")

        # Lưu danh sách dòng có dữ liệu để sử dụng sau này
        temp_dir = tempfile.gettempdir()
        rows_data_file = os.path.join(temp_dir, f"rows_with_data_{sheet_title}.json")
        with open(rows_data_file, "w") as f:
            json.dump({"rows": list(rows_with_data)}, f)

        # Tính start_row cho chế độ Append
        if append_mode:
            # LƯU Ý: Cập nhật cơ chế tìm dòng "More" màu đỏ

            # Bước 1: Tìm tất cả vị trí có chữ "More" trong cột C
            range_name = f"'{sheet_title}'!C:C"
            request = gs_service.spreadsheets().values().get(
                spreadsheetId=source_id,
                range=range_name,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            result = execute_with_retry(request)
            values = result.get('values', [])

            more_rows = []
            for idx, row in enumerate(values):
                if row and row[0] and str(row[0]).strip().lower() == "more":
                    more_rows.append(idx)

            # Bước 2: Kiểm tra màu nền của các dòng có chữ "More"
            if more_rows:
                print(f"Found {len(more_rows)} 'More' rows in sheet {sheet_title}")

                # Lấy thông tin định dạng của các dòng có "More"
                red_row_idx = None
                for more_row in more_rows:
                    check_range = f"'{sheet_title}'!A{more_row + 1}:C{more_row + 1}"
                    request = gs_service.spreadsheets().get(
                        spreadsheetId=source_id,
                        ranges=[check_range],
                        fields="sheets(data(rowData(values(userEnteredFormat/backgroundColor))))"
                    )
                    format_result = execute_with_retry(request)

                    try:
                        sheet_data = format_result.get("sheets", [{}])[0].get("data", [{}])[0].get("rowData", [])
                        if sheet_data:
                            row_data = sheet_data[0]
                            values = row_data.get("values", [])
                            if values:
                                # Kiểm tra nếu ít nhất một ô trong dòng có màu đỏ
                                has_red = False
                                for cell in values:
                                    bg_color = cell.get("userEnteredFormat", {}).get("backgroundColor", {})
                                    red = bg_color.get("red", 0)
                                    green = bg_color.get("green", 0)
                                    blue = bg_color.get("blue", 0)
                                    if red > 0.9 and green < 0.1 and blue < 0.1:
                                        has_red = True
                                        break

                                if has_red:
                                    red_row_idx = more_row
                                    red_row_found = True
                                    print(f"Found red 'More' row at index {red_row_idx+1}")
                                    break
                    except Exception as e:
                        print(f"Error checking cell format for row {more_row+1}: {str(e)}")

                if red_row_idx is not None:
                    # Bắt đầu copy từ dòng sau dòng màu đỏ
                    start_row = red_row_idx + 2  # +2 vì: +1 để chuyển từ 0-based sang 1-based, +1 để nhảy xuống dòng kế tiếp
                    print(f"Will start copying from row {start_row} (after red 'More' row)")

            # Nếu không tìm thấy dòng "More" màu đỏ, tiếp tục với cơ chế hiện tại
            if not red_row_found:
                print(f"No red 'More' row found in sheet {sheet_title}, using default mechanism")

                # Kiểm tra "More" ở cột C (cơ chế cũ)
                last_more_row = None
                for idx, row in enumerate(values):
                    if row and row[0]:  # Đảm bảo row[0] tồn tại và không rỗng
                        # Chuyển giá trị thành chuỗi và kiểm tra
                        cell_value = str(row[0]).strip().lower()
                        if cell_value == "more":
                            last_more_row = idx + 1
                if last_more_row is not None:
                    start_row = last_more_row + 1

            # Kiểm tra màu đỏ nếu không tìm thấy "More"
            if start_row == 1:
                range_name = f"'{sheet_title}'!A:C"
                request = gs_service.spreadsheets().get(
                    spreadsheetId=source_id,
                    ranges=[range_name],
                    fields="sheets(data(rowData(values(userEnteredFormat/backgroundColor))))"
                )
                result = execute_with_retry(request)
                sheet_data = result.get("sheets", [{}])[0].get("data", [{}])[0].get("rowData", [])
                for idx, row_data in enumerate(sheet_data):
                    values = row_data.get("values", [])
                    for cell in values:
                        bg_color = cell.get("userEnteredFormat", {}).get("backgroundColor", {})
                        red = bg_color.get("red", 0)
                        green = bg_color.get("green", 0)
                        blue = bg_color.get("blue", 0)
                        if red == 1 and green == 0 and blue == 0:
                            start_row = idx + 2
                            break
                    if start_row > 1:
                        break

            # Lọc lại danh sách dòng có dữ liệu để chỉ bao gồm các dòng sau start_row
            filtered_rows = [row for row in rows_with_data if row >= start_row]
            if filtered_rows:
                row_count = len(filtered_rows)
                print(f"Filtered rows after start_row {start_row}: {row_count} rows with data")

                # Cập nhật lại file JSON với danh sách dòng đã lọc
                with open(rows_data_file, "w") as f:
                    json.dump({"rows": filtered_rows}, f)

    except Exception as e:
        error_msg = f"Network error: {str(e)}"
        print(f"Error processing sheet {sheet_title}: {error_msg}")

    return sheet_title, start_row, row_count, error_msg, red_row_found  # Thêm flag để biết đã tìm thấy dòng màu đỏ

# ---------------------- Worker Threads ----------------------
class CopyWorker(QThread):
    update_signal = pyqtSignal(str)
    batch_update_signal = pyqtSignal(dict)  # Thêm signal mới cho batch update
    error_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(dict)

    def __init__(self, source_input, dest_input, selected_source_sheets, mapping=None, start_rows=None, preserve_number_format=True):
        super().__init__()
        self.source_input = source_input
        self.dest_input = dest_input
        self.selected_source_sheets = selected_source_sheets
        self.mapping = mapping
        self.start_rows = start_rows
        self.preserve_number_format = preserve_number_format

    def run(self):
        try:
            # Hiển thị thông tin các sheet sẽ copy
            for sheet in self.selected_source_sheets:
                self.update_signal.emit(f"Copying data from sheet {sheet}...")
                time.sleep(0.2)  # Giảm delay để tăng tốc độ UI

            # Định nghĩa callback cho thông tin batch
            def batch_callback(batch_info):
                self.batch_update_signal.emit(batch_info)

            # Thêm cơ chế retry cho run_core_logic
            max_retries = 5  # Tăng số lần retry
            retry_count = 0

            while retry_count < max_retries:
                try:
                    self.update_signal.emit(f"Đang xử lý dữ liệu...")
                    report = run_core_logic(
                        self.source_input,
                        self.dest_input,
                        self.selected_source_sheets,
                        BASE64_CREDENTIALS,
                        custom_mapping=self.mapping,
                        start_rows=self.start_rows,
                        user_choice="append_to_next_row" if self.start_rows else "clear_data",
                        preserve_number_format=self.preserve_number_format,
                        batch_callback=batch_callback  # Truyền callback để nhận thông tin batch
                    )
                    self.finished_signal.emit(report)
                    return  # Thoát khi thành công
                except Exception as e:
                    retry_count += 1
                    error = str(e)

                    if "timeout" in error.lower() or "connection" in error.lower() or "refused" in error.lower():
                        # Lỗi mạng, thử lại
                        if retry_count < max_retries:
                            self.update_signal.emit(f"Lỗi kết nối, đang thử lại ({retry_count}/{max_retries})...")
                            # Thông báo lỗi qua batch_update_signal
                            self.batch_update_signal.emit({
                                "error": f"Lỗi kết nối ({retry_count}/{max_retries}): {error}. Đang thử lại..."
                            })
                            time.sleep(5)  # Đợi lâu hơn cho mỗi lần retry
                        else:
                            # Hết số lần thử
                            raise Exception(f"Lỗi kết nối sau {max_retries} lần thử: {error}")
                    else:
                        # Lỗi khác, không thử lại
                        raise e
        except Exception:
            self.error_signal.emit(traceback.format_exc())

class DryRunWorker(QThread):
    finished_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    def __init__(self, source_input, dest_input, selected_source_sheets, mapping=None):
        super().__init__()
        self.source_input = source_input
        self.dest_input = dest_input
        self.selected_source_sheets = selected_source_sheets
        self.mapping = mapping
    def run(self):
        try:
            report = {"dry_run_data": {"status": "simulated", "rows": 100}}
            self.finished_signal.emit(report)
        except Exception:
            self.error_signal.emit(traceback.format_exc())

# ---------------------- UI Pages ----------------------
class CopyDataPage(QWidget):
    proceed = pyqtSignal(dict)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.gs_manager = GoogleSheetManager(auth_type='oauth', credentials_data=BASE64_CREDENTIALS)
        self.gs_service = self.gs_manager.get_sheets_service()
        self.mapping = {}
        self.sheet_mappings = {}
        self.sheet_row_counts = {}
        self.start_rows = {}
        self.dest_sheet_id = None
        self.dest_sheet_title = None
        self.checkboxes = {}
        self.loading_dialog = None
        self.initUI()
        self.worker = None
        self.progress_dialog = None

    def initUI(self):
        main_layout = QVBoxLayout(self)

        # Mode selection (Create New or Append)
        mode_group = QGroupBox("Mode")
        mode_layout = QHBoxLayout()
        self.create_new_radio = QRadioButton("Tạo mới")
        self.create_new_radio.setChecked(True)
        self.append_radio = QRadioButton("Bổ sung dữ liệu")
        mode_layout.addWidget(self.create_new_radio)
        mode_layout.addWidget(self.append_radio)
        mode_group.setLayout(mode_layout)
        main_layout.addWidget(mode_group)

        # Source Input Group
        source_group = QGroupBox("Source Spreadsheet")
        source_form = QFormLayout()
        hbox_source = QHBoxLayout()
        self.source_edit = QLineEdit()
        self.source_edit.setPlaceholderText("Paste URL or ID of Source")
        hbox_source.addWidget(self.source_edit)
        self.load_source_btn = QPushButton("Load Sheets")
        self.load_source_btn.clicked.connect(self.loadSourceSheets)
        hbox_source.addWidget(self.load_source_btn)
        source_form.addRow("Source URL/ID:", hbox_source)
        source_group.setLayout(source_form)
        main_layout.addWidget(source_group)

        # Source Sheet Selection with Scroll - balanced height
        self.source_sheet_group = QGroupBox("List of Sheets")
        self.source_sheet_group.setMinimumHeight(150)  # Moderate minimum height
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_widget = QWidget()
        self.source_sheet_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_area.setWidget(self.scroll_widget)
        self.source_sheet_group_layout = QVBoxLayout()
        self.source_sheet_group_layout.addWidget(self.scroll_area)
        self.source_sheet_group.setLayout(self.source_sheet_group_layout)
        main_layout.addWidget(self.source_sheet_group, 2)  # Equal stretch factor as log

        # Destination Group
        dest_group = QGroupBox("Destination Spreadsheet")
        dest_form = QFormLayout()
        hbox_dest = QHBoxLayout()
        self.dest_edit = QLineEdit()
        self.dest_edit.setPlaceholderText("Paste URL or ID of Destination")
        hbox_dest.addWidget(self.dest_edit)
        self.dest_sheet_dropdown = QComboBox()
        self.dest_sheet_dropdown.setEnabled(False)
        self.refresh_dest_btn = QPushButton("Refresh")
        self.refresh_dest_btn.clicked.connect(self.loadDestSheets)
        hbox_dest.addWidget(self.dest_sheet_dropdown)
        hbox_dest.addWidget(self.refresh_dest_btn)
        dest_form.addRow("Destination URL/ID:", hbox_dest)
        dest_group.setLayout(dest_form)
        main_layout.addWidget(dest_group)

        # Log - chuyển thành GroupBox
        log_group = QGroupBox("Log Processing")
        log_layout = QVBoxLayout()
        self.log_edit = QTextEdit()
        self.log_edit.setReadOnly(True)
        self.log_edit.setMinimumHeight(150)  # Same minimum height as sheet group
        log_layout.addWidget(self.log_edit)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group, 2)  # Equal stretch factor as sheet group

        # Buttons - remove validate button, only keep copy button
        btn_layout = QHBoxLayout()
        self.copy_button = QPushButton("Copy Data")
        self.copy_button.clicked.connect(self.handleCopy)
        btn_layout.addWidget(self.copy_button)
        main_layout.addLayout(btn_layout)

        self.setLayout(main_layout)
        self.source_edit.textChanged.connect(self.onSourceTextChanged)
        self.dest_edit.textChanged.connect(self.onDestTextChanged)
        self.create_new_radio.toggled.connect(self.resetUIOnModeChange)
        self.append_radio.toggled.connect(self.resetUIOnModeChange)

    def resetUIOnModeChange(self):
        self.source_edit.clear()
        self.dest_edit.clear()
        self.dest_sheet_dropdown.clear()
        self.dest_sheet_dropdown.setEnabled(self.append_radio.isChecked())
        self.sheet_mappings = {}
        self.sheet_row_counts = {}
        self.start_rows = {}
        self.checkboxes = {}
        self.dest_sheet_id = None
        self.dest_sheet_title = None

        for i in reversed(range(self.source_sheet_layout.count())):
            layout_item = self.source_sheet_layout.itemAt(i)
            if layout_item.layout():
                hbox = layout_item.layout()
                for j in reversed(range(hbox.count())):
                    widget = hbox.itemAt(j).widget()
                    if widget:
                        widget.setParent(None)
                hbox.setParent(None)
            self.source_sheet_layout.removeItem(layout_item)

        if self.append_radio.isChecked():
            self.loadDestSheets()

    def onSourceTextChanged(self, text):
        new_text = self.extract_id(text)
        if new_text != text:
            self.source_edit.blockSignals(True)
            self.source_edit.setText(new_text)
            self.source_edit.blockSignals(False)

    def onDestTextChanged(self, text):
        new_text = self.extract_id(text)
        if new_text != text:
            self.dest_edit.blockSignals(True)
            self.dest_edit.setText(new_text)
            self.dest_edit.blockSignals(False)
        if self.append_radio.isChecked():
            self.loadDestSheets()

    def extract_id(self, text):
        text = text.strip()
        import re
        if "spreadsheets/d/" in text:
            match = re.search(r'/spreadsheets/d/([a-zA-Z0-9-_]+)', text)
            if match:
                return match.group(1)
        return text

    def loadDestSheets(self):
        dest_id = self.dest_edit.text().strip()
        if not dest_id:
            return

        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # Thiết lập request với timeout dài hơn
                request = self.gs_service.spreadsheets().get(
                    spreadsheetId=dest_id,
                    fields="sheets(properties(title,sheetId))"
                )
                # Tăng timeout cho request
                request.http.timeout = 60  # 60 giây

                result = request.execute()
                sheets = result.get("sheets", [])
                self.dest_sheet_dropdown.clear()
                pool_deal_index = -1

                for idx, sheet in enumerate(sheets):
                    sheet_title = sheet["properties"]["title"]
                    self.dest_sheet_dropdown.addItem(sheet_title)
                    if sheet_title == "Pool Deal":
                        pool_deal_index = idx

                if pool_deal_index != -1:
                    self.dest_sheet_dropdown.setCurrentIndex(pool_deal_index)

                # Thành công, thoát vòng lặp
                return
            except Exception as e:
                retry_count += 1
                error_msg = str(e)

                if retry_count < max_retries:
                    # Log và thử lại
                    print(f"Lỗi kết nối destination, thử lại lần {retry_count}/{max_retries}: {error_msg}")
                    self.log_edit.append(f"Kết nối không ổn định, đang thử lại ({retry_count}/{max_retries})...\n")
                    time.sleep(2)  # Đợi 2 giây trước khi thử lại
                else:
                    # Hết số lần thử
                    self.log_edit.append(f"Error loading destination sheets: {error_msg}\n")

    def loadSourceSheets(self):
        # Clear existing checkboxes first
        for i in reversed(range(self.source_sheet_layout.count())):
            layout_item = self.source_sheet_layout.itemAt(i)
            if layout_item.layout():
                for j in reversed(range(layout_item.layout().count())):
                    widget = layout_item.layout().itemAt(j).widget()
                    if widget:
                        widget.setParent(None)
                layout_item.layout().setParent(None)
            elif layout_item.widget():
                layout_item.widget().setParent(None)
            self.source_sheet_layout.removeItem(layout_item)

        source_id = self.source_edit.text().strip()
        if not source_id:
            QMessageBox.warning(self, "Input Error", "Please enter the URL/ID of the Source!")
            return

        self.loading_dialog = LoadingDialog(self)
        self.loading_dialog.show()
        QApplication.processEvents()

        self.loader = SheetLoader(self.gs_service, source_id)
        self.loader.loaded.connect(self.onSheetsLoaded)
        self.loader.error.connect(self.onLoadError)
        self.loader.start()

    def onSheetsLoaded(self, sheets):
        self.sheet_row_counts = {}
        self.start_rows = {}
        self.checkboxes = {}
        excluded_sheets = ["Hướng dẫn sử dụng file", "Template input deal (Vietnamese)", "Pool Deal"]
        append_mode = self.append_radio.isChecked()

        # Create a grid layout to replace the existing layout in source_sheet_layout
        grid_layout = QGridLayout()

        # Clear existing layout first
        for i in reversed(range(self.source_sheet_layout.count())):
            layout_item = self.source_sheet_layout.itemAt(i)
            if layout_item.layout():
                for j in reversed(range(layout_item.layout().count())):
                    widget = layout_item.layout().itemAt(j).widget()
                    if widget:
                        widget.setParent(None)
                layout_item.layout().setParent(None)
            elif layout_item.widget():
                layout_item.widget().setParent(None)
            self.source_sheet_layout.removeItem(layout_item)

        # Add grid layout to main layout
        self.source_sheet_layout.addLayout(grid_layout)

        # Define the number of columns
        max_cols = 3

        # Prepare filtered sheet list
        filtered_sheets = []
        for sheet in sheets:
            sheet_title = sheet["properties"]["title"]
            if sheet_title not in excluded_sheets:
                filtered_sheets.append(sheet_title)

        # Add checkboxes to grid
        row, col = 0, 0
        tasks = []
        for sheet_title in filtered_sheets:
            checkbox = QCheckBox(sheet_title)
            checkbox.setFixedHeight(30)
            grid_layout.addWidget(checkbox, row, col)
            self.checkboxes[sheet_title] = checkbox
            self.sheet_row_counts[sheet_title] = 0  # Giá trị tạm thời

            tasks.append((sheet_title, append_mode))

            # Update grid position for next checkbox
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # Cập nhật total_sheets trước khi bắt đầu xử lý
        self.total_sheets = len(tasks)
        if self.total_sheets == 0:
            self.log_edit.append("No sheets to process.\n")
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
            return

        # Cập nhật lại LoadingDialog với số lượng sheet chính xác
        if self.loading_dialog:
            self.loading_dialog.close()
        self.loading_dialog = LoadingDialog(self, self.total_sheets)
        self.loading_dialog.show()
        QApplication.processEvents()

        # Bắt đầu xử lý tuần tự
        self.current_sheet_index = 0
        self.tasks = tasks
        self.process_next_sheet()

    def process_next_sheet(self):
        # Kiểm tra nếu đã xử lý hết tất cả sheet
        if self.current_sheet_index >= len(self.tasks):
            self.log_edit.append(f"Successfully loaded {len(self.checkboxes)} sheets.\n")
            if self.loading_dialog:
                self.loading_dialog.close()
                self.loading_dialog = None
            return

        sheet_title, append_mode = self.tasks[self.current_sheet_index]

        try:
            # Cập nhật tiến trình trước khi xử lý
            self.loading_dialog.update_progress(self.current_sheet_index + 1)

            # Xử lý sheet
            sheet_title, start_row, row_count, error_msg, red_row_found = process_sheet_data(
                self.gs_service, self.source_edit.text().strip(), sheet_title, append_mode
            )
            self.sheet_row_counts[sheet_title] = row_count
            if append_mode:
                self.start_rows[sheet_title] = start_row
                # Lưu thông tin về việc tìm thấy dòng đỏ
                if red_row_found:
                    if not hasattr(self, 'red_rows_found'):
                        self.red_rows_found = {}
                    self.red_rows_found[sheet_title] = True
                    self.log_edit.append(f"Chế độ Append: Đã tìm thấy dòng 'More' màu đỏ trong sheet {sheet_title}, sẽ bắt đầu từ dòng {start_row}.\n")
            if error_msg:
                self.log_edit.append(f"Error processing sheet {sheet_title}: {error_msg}\n")

        except Exception as e:
            self.log_edit.append(f"Error processing sheet {sheet_title}: {str(e)}\n")

        # Tăng chỉ số sheet và tiếp tục xử lý sheet tiếp theo
        self.current_sheet_index += 1
        QTimer.singleShot(1000, self.process_next_sheet)  # Delay 1 giây

    def onLoadError(self, error_message):
        self.log_edit.append(f"Error loading sheets: {error_message}\n")
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None
        QMessageBox.critical(self, "Error", f"Error loading sheets: {error_message}")

    def createPoolDeal(self):
        source_id = self.source_edit.text().strip()
        dest_id = self.dest_edit.text().strip()
        if not source_id or not dest_id:
            QMessageBox.warning(self, "Input Error", "Please enter both Source and Destination!")
            return

        selected_sheets = [sheet_title for sheet_title, checkbox in self.checkboxes.items() if checkbox.isChecked()]
        if not selected_sheets:
            QMessageBox.warning(self, "Input Error", "Please select at least one source sheet!")
            return

        if self.create_new_radio.isChecked():
            self.log_edit.append(f"Starting to create new Pool Deal sheet in {dest_id}...\n")
            try:
                self.log_edit.append(f"Fetching template from {TEMPLATE_SPREADSHEET_ID}...\n")
                template_result = self.gs_service.spreadsheets().get(
                    spreadsheetId=TEMPLATE_SPREADSHEET_ID,
                    fields="sheets(properties(title,sheetId))"
                ).execute()
                template_sheets = template_result.get("sheets", [])
                template_sheet_meta = next((s for s in template_sheets if s["properties"]["title"] == TEMPLATE_SHEET_TITLE), None)
                if not template_sheet_meta:
                    self.log_edit.append(f"Error: Template sheet '{TEMPLATE_SHEET_TITLE}' not found!\n")
                    QMessageBox.critical(self, "Error", f"Template sheet '{TEMPLATE_SHEET_TITLE}' not found!")
                    return
                template_sheet_id = template_sheet_meta["properties"]["sheetId"]
                self.log_edit.append(f"Found template sheet '{TEMPLATE_SHEET_TITLE}' with ID {template_sheet_id}.\n")

                self.log_edit.append(f"Copying template to destination {dest_id}...\n")
                self.dest_sheet_id, self.dest_sheet_title = copy_sheet_template(
                    self.gs_service, TEMPLATE_SPREADSHEET_ID, template_sheet_id, dest_id, TEMPLATE_SHEET_TITLE
                )
                if not self.dest_sheet_id:
                    self.log_edit.append(f"Error: Could not create Pool Deal sheet!\n")
                    QMessageBox.critical(self, "Error", "Could not create Pool Deal sheet!")
                    return
                self.log_edit.append(f"Successfully copied template, new sheet ID: {self.dest_sheet_id}, title: {self.dest_sheet_title}.\n")

            except Exception as e:
                self.log_edit.append(f"Error creating Pool Deal sheet: {e}\n")
                QMessageBox.critical(self, "Error", f"Error creating Pool Deal sheet: {e}")
                return

            try:
                self.log_edit.append(f"Loading headers from template '{TEMPLATE_SHEET_TITLE}'...\n")
                dest_headers = get_headers_from_sheet(self.gs_service, TEMPLATE_SPREADSHEET_ID, TEMPLATE_SHEET_TITLE)
                if not dest_headers:
                    self.log_edit.append(f"Error: Could not load headers from template!\n")
                    QMessageBox.critical(self, "Error", "Could not load headers from template!")
                    return
                self.log_edit.append(f"Successfully loaded {len(dest_headers)} headers from template.\n")
            except Exception as e:
                self.log_edit.append(f"Error loading headers from template: {e}\n")
                QMessageBox.critical(self, "Error", f"Error loading headers from template: {e}")
                return

            self.sheet_mappings = {}
            for sheet_title in selected_sheets:
                try:
                    self.log_edit.append(f"Loading headers for source sheet '{sheet_title}'...\n")
                    source_headers = get_headers_from_sheet(self.gs_service, source_id, sheet_title)
                    if source_headers:
                        mapping = auto_map_columns(source_headers, dest_headers)
                        if mapping:
                            self.sheet_mappings[sheet_title] = mapping
                            self.log_edit.append(f"Successfully mapped {len(mapping)} columns for sheet '{sheet_title}'.\n")
                except Exception as ex:
                    self.log_edit.append(f"Error loading headers for {sheet_title}: {ex}\n")

            total_rows = sum(self.sheet_row_counts.get(sheet, 0) for sheet in selected_sheets)
            total_rows_with_buffer = total_rows + 5

            try:
                self.log_edit.append(f"Extending rows in {self.dest_sheet_title} with {total_rows_with_buffer} rows...\n")
                extend_rows_with_formatting(
                    self.gs_service,
                    dest_id,
                    self.dest_sheet_id,
                    self.dest_sheet_title,
                    start_row=5,
                    num_rows=total_rows_with_buffer,
                    num_columns=len(dest_headers)
                )
                self.log_edit.append(f"Successfully extended {total_rows_with_buffer} rows with formatting.\n")
            except Exception as e:
                self.log_edit.append(f"Error extending rows in Pool Deal: {e}\n")
                QMessageBox.critical(self, "Error", f"Error extending rows in Pool Deal: {e}")
                return
        else:
            self.dest_sheet_title = self.dest_sheet_dropdown.currentText()
            if not self.dest_sheet_title:
                QMessageBox.warning(self, "Input Error", "Please select a destination sheet!")
                return

            try:
                self.log_edit.append(f"Finding sheet '{self.dest_sheet_title}' in destination {dest_id}...\n")
                result = self.gs_service.spreadsheets().get(
                    spreadsheetId=dest_id,
                    fields="sheets(properties(title,sheetId))"
                ).execute()
                sheets = result.get("sheets", [])
                for sheet in sheets:
                    if sheet["properties"]["title"] == self.dest_sheet_title:
                        self.dest_sheet_id = sheet["properties"]["sheetId"]
                        break
                if not self.dest_sheet_id:
                    self.log_edit.append(f"Error: Could not find sheet {self.dest_sheet_title} in destination!\n")
                    QMessageBox.critical(self, "Error", f"Could not find sheet {self.dest_sheet_title} in destination!")
                    return
                self.log_edit.append(f"Found sheet '{self.dest_sheet_title}' with ID {self.dest_sheet_id}.\n")
            except Exception as e:
                self.log_edit.append(f"Error finding destination sheet: {e}\n")
                QMessageBox.critical(self, "Error", f"Error finding destination sheet: {e}")
                return

            try:
                self.log_edit.append(f"Loading headers from '{self.dest_sheet_title}'...\n")
                dest_headers = get_headers_from_sheet(self.gs_service, dest_id, self.dest_sheet_title)
                if not dest_headers:
                    self.log_edit.append(f"Error: Could not load headers from {self.dest_sheet_title}!\n")
                    QMessageBox.critical(self, "Error", f"Could not load headers from {self.dest_sheet_title}!\n")
                    return
                self.log_edit.append(f"Successfully loaded {len(dest_headers)} headers from {self.dest_sheet_title}.\n")
            except Exception as e:
                self.log_edit.append(f"Error loading headers from {self.dest_sheet_title}: {e}\n")
                QMessageBox.critical(self, "Error", f"Error loading headers from {self.dest_sheet_title}: {e}")
                return

            self.sheet_mappings = {}
            for sheet_title in selected_sheets:
                try:
                    self.log_edit.append(f"Loading headers for source sheet '{sheet_title}'...\n")
                    source_headers = get_headers_from_sheet(self.gs_service, source_id, sheet_title)
                    if source_headers:
                        mapping = auto_map_columns(source_headers, dest_headers)
                        if mapping:
                            self.sheet_mappings[sheet_title] = mapping
                            self.log_edit.append(f"Successfully mapped {len(mapping)} columns for sheet '{sheet_title}'.\n")
                except Exception as ex:
                    self.log_edit.append(f"Error loading headers for {sheet_title}: {ex}\n")

        # Log 'More' hoặc start_row cho chế độ Append tại đây
        if self.append_radio.isChecked():
            for sheet_title in selected_sheets:
                start_row = self.start_rows.get(sheet_title)
                if start_row:
                    try:
                        range_name = f"'{sheet_title}'!C:C"
                        result = self.gs_service.spreadsheets().values().get(
                            spreadsheetId=source_id,
                            range=range_name,
                            valueRenderOption='UNFORMATTED_VALUE'
                        ).execute()
                        values = result.get('values', [])
                        last_more_row = None
                        for idx, row in enumerate(values):
                            if row and row[0]:  # Đảm bảo row[0] tồn tại
                                cell_value = str(row[0]).strip().lower()
                                if cell_value == "more":
                                    last_more_row = idx + 1
                        if last_more_row is not None and last_more_row + 1 == start_row:
                            self.log_edit.append(f"Found 'More' in sheet {sheet_title} at row {last_more_row}, starting from row {start_row}\n")
                    except Exception as e:
                        self.log_edit.append(f"Error confirming 'More' in sheet {sheet_title}: {e}\n")

    def handleCopy(self):
        source = self.source_edit.text().strip()
        dest = self.dest_edit.text().strip()
        selected_sheets = [sheet_title for sheet_title, checkbox in self.checkboxes.items() if checkbox.isChecked()]

        # If destination is empty, use source ID as default destination
        if not dest and source:
            dest = source
            self.dest_edit.setText(dest)
            self.log_edit.append(f"Using source spreadsheet as destination: {dest}\n")

        if not source or not selected_sheets:
            QMessageBox.warning(self, "Input Error", "Please fill in source and select at least one sheet!")
            return

        # Thêm thông báo về việc giữ nguyên định dạng số tiền
        self.log_edit.append("Chú ý: Các cột số tiền sẽ được giữ nguyên định dạng (bao gồm dấu phẩy, dấu chấm) từ sheet nguồn\n")

        # Store destination info in a temp file for Step 2
        try:
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, "raw_processed_dest.txt")
            with open(temp_file, "w", encoding="utf-8") as f:
                f.write(f"{dest}\n")  # First line: spreadsheet ID
                # Đảm bảo lưu tên sheet ngay cả khi chưa tạo sheet
                sheet_title = self.dest_sheet_title if self.dest_sheet_title else "Pool Deal"
                f.write(f"{sheet_title}\n")  # Second line: sheet title
            self.log_edit.append(f"Stored destination info for Step 2.\n")
        except Exception as e:
            self.log_edit.append(f"Warning: Could not store destination info: {str(e)}\n")

        # First create the Pool Deal sheet
        self.createPoolDeal()

        if not self.sheet_mappings:
            QMessageBox.warning(self, "Error", "Could not create mapping for any sheet!")
            return

        self.copy_button.setEnabled(False)
        self.progress_dialog = ProgressDialog(self)
        self.progress_dialog.show()
        self.log_edit.append(f"Starting copy process for sheets: {', '.join(selected_sheets)}\n")
        self.worker = CopyWorker(
            source, dest, selected_sheets,
            mapping=self.sheet_mappings,
            start_rows=self.start_rows if self.append_radio.isChecked() else None,
            preserve_number_format=True  # Luôn dùng True để giữ nguyên định dạng
        )
        self.worker.update_signal.connect(self.progress_dialog.update_info)

        # Kết nối signal mới cho thông tin batch
        self.worker.batch_update_signal.connect(self.update_batch_progress)

        self.worker.finished_signal.connect(self.onCopyFinished)
        self.worker.error_signal.connect(self.onCopyError)
        self.worker.start()

    def update_batch_progress(self, batch_info):
        """Xử lý thông tin về tiến trình xử lý batch"""
        if "error" in batch_info:
            # Hiển thị lỗi trong progress_dialog
            self.progress_dialog.update_error(batch_info["error"])
            # Cũng ghi vào log chính
            self.log_edit.append(f"Lỗi: {batch_info['error']}\n")
        elif "batch_index" in batch_info:
            # Cập nhật thông tin batch hiện tại (không cần ghi log)
            self.progress_dialog.update_batch_info(batch_info)
        elif "completion" in batch_info:
            # Khi hoàn thành tất cả các batch, hiển thị thông tin tổng hợp
            expected = batch_info.get("expected_rows", 0)
            actual = batch_info.get("actual_rows", 0)
            if expected != actual:
                self.progress_dialog.update_error(
                    f"⚠️ Cảnh báo: Chỉ có {actual}/{expected} dòng được cập nhật thành công. "
                    f"Có thể do lỗi kết nối hoặc API limits."
                )
                self.log_edit.append(f"Cảnh báo: Chỉ có {actual}/{expected} dòng được cập nhật thành công\n")

    def onCopyError(self, error_msg):
        self.copy_button.setEnabled(True)
        if self.progress_dialog:
            # Cập nhật lỗi trong dialog trước khi đóng
            self.progress_dialog.update_error("Lỗi nghiêm trọng, quá trình copy bị hủy bỏ. Xem log để biết chi tiết.")
            time.sleep(1)  # Cho phép người dùng thấy lỗi trước khi đóng dialog
            self.progress_dialog.close()
        self.log_edit.append("Error during copy process:\n" + error_msg)
        QMessageBox.critical(self, "Error", f"Error during copy process:\n{error_msg}")

    def onCopyFinished(self, report):
        self.copy_button.setEnabled(True)
        self.progress_dialog.close()

        # Kiểm tra nếu không có dữ liệu để copy sau khi lọc
        if report.get("status") == "no_data_to_copy":
            self.log_edit.append("Không có dữ liệu để copy sau khi lọc các dòng\n")
            self.log_edit.append("Chương trình chỉ copy dữ liệu từ các dòng có dữ liệu trong cột E, F, G hoặc H\n")
            QMessageBox.warning(self, "Không có dữ liệu", "Không có dữ liệu để copy sau khi lọc.\nChương trình chỉ copy dữ liệu từ các dòng có dữ liệu trong cột E, F, G hoặc H.")
            return

        if 'total_rows' in report:
            self.log_edit.append(f"Copy process completed. Total rows processed: {report['total_rows']}\n")
            # Thêm thông báo về việc giữ nguyên định dạng số
            self.log_edit.append("✓ Đã giữ nguyên định dạng số (bao gồm dấu phẩy và dấu chấm) từ sheet nguồn sang sheet đích.\n")
        else:
            self.log_edit.append("Copy process completed, but 'total_rows' is missing in the report.\n")

        report["source_input"] = self.source_edit.text().strip()
        report["dest_input"] = self.dest_edit.text().strip()
        report["selected_source_sheets"] = [sheet_title for sheet_title, checkbox in self.checkboxes.items() if checkbox.isChecked()]

        # Thêm thông tin spreadsheet ID và sheet title vào report
        dest_id = self.dest_edit.text().strip()
        if not dest_id:
            dest_id = self.source_edit.text().strip()

        report["dest_spreadsheet_id"] = dest_id
        report["dest_sheet_title"] = self.dest_sheet_title if self.dest_sheet_title else "Pool Deal"

        # Lưu lại thông tin một lần nữa để đảm bảo
        try:
            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, "raw_processed_dest.txt")
            with open(temp_file, "w", encoding="utf-8") as f:
                f.write(f"{dest_id}\n")
                sheet_title = self.dest_sheet_title if self.dest_sheet_title else "Pool Deal"
                f.write(f"{sheet_title}\n")
            self.log_edit.append(f"Updated destination info for Step 2: {dest_id}, {sheet_title}\n")
        except Exception as e:
            self.log_edit.append(f"Warning: Could not update destination info: {str(e)}\n")

        self.report = report

        # Thêm dòng màu đỏ vào các sheet nguồn đã copy
        source_id = self.source_edit.text().strip()
        selected_sheets = [sheet_title for sheet_title, checkbox in self.checkboxes.items() if checkbox.isChecked()]
        is_append_mode = self.append_radio.isChecked()

        self.log_edit.append(f"Đang thêm dòng đánh dấu màu đỏ vào các sheet đã copy ({len(selected_sheets)} sheets)...\n")

        if is_append_mode:
            self.log_edit.append("Chế độ Append: Sẽ xóa dòng đỏ cũ (nếu có) trước khi thêm dòng mới.\n")

        marked_sheets = []

        for sheet_title in selected_sheets:
            # Xác định xem đã tìm thấy dòng đỏ trong sheet này chưa
            red_row_found = hasattr(self, 'red_rows_found') and sheet_title in self.red_rows_found

            if is_append_mode and red_row_found:
                self.log_edit.append(f"Sheet {sheet_title}: Đã tìm thấy dòng đỏ trước đó, sẽ xóa và thêm dòng mới.\n")

            # Thêm dòng đánh dấu màu đỏ, truyền thông tin về chế độ append và dòng đỏ đã tìm thấy
            if self.add_red_row_marker(
                    self.gs_service,
                source_id,
                sheet_title,
                self.sheet_row_counts,
                append_mode=is_append_mode,
                red_row_found=red_row_found
            ):
                marked_sheets.append(sheet_title)

        if marked_sheets:
            self.log_edit.append(f"Đã thêm đánh dấu màu đỏ cho {len(marked_sheets)}/{len(selected_sheets)} sheet.\n")
            # Cập nhật report với thông tin về các sheet đã đánh dấu
            report["marked_sheets"] = marked_sheets
        else:
            self.log_edit.append("Không thể thêm đánh dấu màu đỏ cho bất kỳ sheet nào.\n")

        # Hiển thị cảnh báo ID liên tiếp nếu có - ĐÃ CHUYỂN XUỐNG CUỐI
        if 'sequential_warnings' in report and report['sequential_warnings']:
            # Tạo định dạng text màu đỏ đậm
            format_warning = QTextCharFormat()
            format_warning.setForeground(QColor(255, 0, 0))  # Đỏ
            format_warning.setFontWeight(700)  # Đậm

            # Hiển thị cảnh báo trong log với một header dễ nhận biết
            self.log_edit.append("\n" + "="*80)
            self.log_edit.append("⚠️⚠️⚠️ CÁC CẢNH BÁO ID LIÊN TIẾP QUAN TRỌNG ⚠️⚠️⚠️")
            self.log_edit.append("="*80 + "\n")

            # Lưu vị trí con trỏ hiện tại
            cursor = self.log_edit.textCursor()

            # Thêm từng cảnh báo với định dạng đặc biệt
            for warning in report['sequential_warnings']:
                cursor.insertText(warning + "\n\n", format_warning)

            self.log_edit.append("="*80)
            self.log_edit.append("⚠️ Vui lòng kiểm tra các ID liên tiếp này - có thể là dấu hiệu của dữ liệu bị sai!")
            self.log_edit.append("="*80 + "\n")

            # Cuộn xuống cuối cùng để người dùng nhìn thấy cảnh báo
            self.log_edit.moveCursor(QTextCursor.End)

            # Hiển thị message box thông báo cho người dùng
            QMessageBox.warning(
                self,
                "Cảnh báo ID liên tiếp",
                f"Đã phát hiện {len(report['sequential_warnings'])} chuỗi ID liên tiếp. Vui lòng kiểm tra log để biết chi tiết."
            )

        # Hiển thị message box thông báo thành công, kèm theo thông tin giữ nguyên định dạng số
        QMessageBox.information(
            self,
            "Hoàn tất sao chép dữ liệu",
            f"Đã sao chép thành công {report.get('total_rows', 0)} dòng dữ liệu.\n"
            f"Định dạng số đã được giữ nguyên từ sheet nguồn."
            )

        # Directly emit proceed signal after copy finishes
        self.proceed.emit(report)

    def add_red_row_marker(self, gs_service, source_id, sheet_title, sheet_row_counts, append_mode=False, red_row_found=False):
        """Thêm dòng đánh dấu màu đỏ vào sheet sau khi copy dữ liệu"""
        try:
            # Xác định vị trí dòng để thêm đánh dấu
            row_count = sheet_row_counts.get(sheet_title, 0)
            if row_count <= 0:
                self.log_edit.append(f"Sheet {sheet_title}: Không có dữ liệu để thêm đánh dấu\n")
                return False

            # Tìm sheet ID
            request = gs_service.spreadsheets().get(
                spreadsheetId=source_id,
                fields="sheets(properties(title,sheetId))"
            )
            result = request.execute()
            sheet_id = None
            for sheet in result["sheets"]:
                if sheet["properties"]["title"] == sheet_title:
                    sheet_id = sheet["properties"]["sheetId"]
                    break

            if not sheet_id:
                self.log_edit.append(f"Sheet {sheet_title}: Không tìm thấy sheet ID\n")
                return False

            # Xác định hàng để đặt đánh dấu
            # Trong chế độ append, nếu đã tìm thấy dòng màu đỏ, sẽ xóa dòng cũ trước
            if append_mode and red_row_found:
                # Tìm vị trí của dòng "More" màu đỏ hiện tại
                range_name = f"'{sheet_title}'!C:C"
                request = gs_service.spreadsheets().values().get(
                    spreadsheetId=source_id,
                    range=range_name,
                    valueRenderOption='UNFORMATTED_VALUE'
                )
                result = request.execute()
                values = result.get('values', [])

                more_row_idx = None
                for idx, row in enumerate(values):
                    if row and row[0] and str(row[0]).strip().lower() == "more":
                        # Kiểm tra màu đỏ
                        check_range = f"'{sheet_title}'!A{idx+1}:C{idx+1}"
                        format_request = gs_service.spreadsheets().get(
                            spreadsheetId=source_id,
                            ranges=[check_range],
                            fields="sheets(data(rowData(values(userEnteredFormat/backgroundColor))))"
                        )
                        format_result = format_request.execute()

                        sheet_data = format_result.get("sheets", [{}])[0].get("data", [{}])[0].get("rowData", [])
                        if sheet_data:
                            row_data = sheet_data[0]
                            cell_values = row_data.get("values", [])
                            if cell_values:
                                for cell in cell_values:
                                    bg_color = cell.get("userEnteredFormat", {}).get("backgroundColor", {})
                                    red = bg_color.get("red", 0)
                                    green = bg_color.get("green", 0)
                                    blue = bg_color.get("blue", 0)
                                    if red > 0.9 and green < 0.1 and blue < 0.1:
                                        more_row_idx = idx
                                        break

                        if more_row_idx is not None:
                            break

                # Nếu tìm thấy, xóa dòng đó
                if more_row_idx is not None:
                    delete_request = {
                        "requests": [
                            {
                                "deleteDimension": {
                                    "range": {
                                        "sheetId": sheet_id,
                                        "dimension": "ROWS",
                                        "startIndex": more_row_idx,
                                        "endIndex": more_row_idx + 1
                                    }
                                }
                            }
                        ]
                    }

                    gs_service.spreadsheets().batchUpdate(
                        spreadsheetId=source_id,
                        body=delete_request
                    ).execute()

                    self.log_edit.append(f"Sheet {sheet_title}: Đã xóa dòng 'More' màu đỏ cũ tại dòng {more_row_idx+1}\n")

            # Tính toán vị trí dòng mới để thêm đánh dấu
            target_row = None

            # Đếm dòng dữ liệu hiện tại
            range_name = f"'{sheet_title}'!G3:G"
            request = gs_service.spreadsheets().values().get(
                spreadsheetId=source_id,
                range=range_name,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            result = request.execute()
            values = result.get('values', [])
            last_data_row = 3  # Bắt đầu từ dòng 3
            for idx, row in enumerate(values):
                if row and row[0] not in [None, ""]:
                    last_data_row = idx + 3  # +3 vì bắt đầu từ G3

            target_row = last_data_row + 1

            # Thêm dòng "More" màu đỏ
            red_color = {
                "red": 1.0,
                "green": 0.0,
                "blue": 0.0
            }

            update_requests = {
                "requests": [
                    # Cập nhật nội dung
                    {
                        "updateCells": {
                            "rows": [
                                {
                                    "values": [
                                        {"userEnteredValue": {"stringValue": ""}},
                                        {"userEnteredValue": {"stringValue": ""}},
                                        {"userEnteredValue": {"stringValue": "More"}}
                                    ]
                                }
                            ],
                            "fields": "userEnteredValue",
                            "start": {
                                "sheetId": sheet_id,
                                "rowIndex": target_row - 1,  # 0-based index
                                "columnIndex": 0
                            }
                        }
                    },
                    # Cập nhật định dạng (màu đỏ cho toàn bộ dòng)
                    {
                        "updateCells": {
                            "rows": [
                                {
                                    "values": [
                                        {"userEnteredFormat": {"backgroundColor": red_color}},
                                        {"userEnteredFormat": {"backgroundColor": red_color}},
                                        {"userEnteredFormat": {"backgroundColor": red_color}}
                                    ]
                                }
                            ],
                            "fields": "userEnteredFormat.backgroundColor",
                            "start": {
                                "sheetId": sheet_id,
                                "rowIndex": target_row - 1,  # 0-based index
                                "columnIndex": 0
                            }
                        }
                    }
                ]
            }

            # Thêm yêu cầu định dạng cho các cột còn lại (nếu có)
            # Tìm số cột tối đa
            max_col_request = gs_service.spreadsheets().get(
                spreadsheetId=source_id,
                ranges=[f"'{sheet_title}'!1:1"],
                fields="sheets(properties(gridProperties(columnCount)))"
            )
            max_col_result = max_col_request.execute()
            max_columns = max_col_result.get("sheets", [{}])[0].get("properties", {}).get("gridProperties", {}).get("columnCount", 10)

            # Nếu cần, thêm yêu cầu để đổi màu các cột còn lại
            if max_columns > 3:
                update_requests["requests"].append({
                    "updateCells": {
                        "rows": [
                            {
                                "values": [{"userEnteredFormat": {"backgroundColor": red_color}} for _ in range(max_columns - 3)]
                            }
                        ],
                        "fields": "userEnteredFormat.backgroundColor",
                        "start": {
                            "sheetId": sheet_id,
                            "rowIndex": target_row - 1,  # 0-based index
                            "columnIndex": 3  # Bắt đầu từ cột D (index 3)
                        }
                    }
                })

            # Thực hiện cập nhật
            gs_service.spreadsheets().batchUpdate(
                spreadsheetId=source_id,
                body=update_requests
            ).execute()

            self.log_edit.append(f"Sheet {sheet_title}: Đã thêm dòng 'More' màu đỏ tại dòng {target_row}\n")
            return True

        except Exception as e:
            self.log_edit.append(f"Sheet {sheet_title}: Lỗi khi thêm dòng đánh dấu: {str(e)}\n")
            return False

# Thêm class ProcessDataPage để tích hợp bước 2
class ProcessDataPage(QWidget):
    proceed = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.gs_manager = GoogleSheetManager(auth_type='oauth', credentials_data=BASE64_CREDENTIALS)
        self.gs_service = self.gs_manager.get_sheets_service()
        self.spreadsheet_id = None
        self.sheet_title = None
        self.headers = []
        self.header_row = 3  # Mặc định, dòng header là dòng 3 (index 2)
        self.header_checkboxes = {}  # Dict để lưu checkbox của từng header
        self.condition_checkboxes = {}  # Dict để lưu checkbox của từng điều kiện cho mỗi header
        self.progress_dialog = None

        # Các loại điều kiện xử lý có sẵn
        self.condition_types = [
            "Clear 'X' Values",
            "Format Numbers",
            "Convert 'k' to '000'",
            "Fix Line Breaks",
            "Format Percentages",
            "Clear Empty Rows"
        ]

        self.initUI()

    def initUI(self):
        main_layout = QVBoxLayout(self)

        # Thông tin sheet từ bước 1 - Thiết kế lại theo yêu cầu
        sheet_info_group = QGroupBox("Spreadsheet Information")
        sheet_info_layout = QHBoxLayout()  # Thay đổi từ QGridLayout sang QHBoxLayout
        sheet_info_layout.setContentsMargins(10, 5, 10, 5)  # Giảm margins

        # Tạo layout cho phần bên trái (Spreadsheet ID và Sheet Title)
        left_layout = QFormLayout()
        left_layout.setContentsMargins(0, 0, 10, 0)
        left_layout.setSpacing(5)

        # Spreadsheet ID và Sheet Title ở phần bên trái
        self.sheet_id_label = QLabel("No spreadsheet selected")
        self.sheet_id_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        left_layout.addRow("Spreadsheet ID:", self.sheet_id_label)

        self.sheet_title_label = QLabel("No sheet selected")
        left_layout.addRow("Sheet Title:", self.sheet_title_label)

        # Tạo layout cho phần bên phải (Header Row và nút Load Headers)
        right_layout = QHBoxLayout()
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(5)

        header_label = QLabel("Header Row:")
        right_layout.addWidget(header_label)

        self.header_row_spin = QComboBox()
        for i in range(1, 11):
            self.header_row_spin.addItem(f"Row {i}")
        self.header_row_spin.setCurrentIndex(2)  # Mặc định là dòng 3
        self.header_row_spin.currentIndexChanged.connect(self.onHeaderRowChanged)
        self.header_row_spin.setFixedWidth(80)
        right_layout.addWidget(self.header_row_spin)

        # Nút Load Headers nhỏ hơn
        self.load_headers_btn = QPushButton("Load Headers")
        self.load_headers_btn.setFixedWidth(100)
        self.load_headers_btn.clicked.connect(self.loadHeaders)
        right_layout.addWidget(self.load_headers_btn)

        # Thêm hai layouts vào sheet_info_layout
        sheet_info_layout.addLayout(left_layout, 2)  # 2/3 rộng cho phần trái
        sheet_info_layout.addLayout(right_layout, 1)  # 1/3 rộng cho phần phải

        sheet_info_group.setLayout(sheet_info_layout)
        main_layout.addWidget(sheet_info_group)

        # Tạo container cho phần Rules và Log
        rules_log_container = QWidget()
        rules_log_layout = QVBoxLayout(rules_log_container)
        rules_log_layout.setContentsMargins(0, 0, 0, 0)

        # Khu vực cho việc chọn headers và điều kiện - Thiết kế mới
        headers_group = QGroupBox("Select Headers and Conditions")
        headers_layout = QVBoxLayout()
        headers_layout.setContentsMargins(5, 5, 5, 5)
        headers_layout.setSpacing(5)

        # Nút điều khiển chung
        control_layout = QHBoxLayout()
        self.select_all_headers_btn = QPushButton("Select All Headers")
        self.select_all_headers_btn.clicked.connect(self.selectAllHeaders)
        self.select_all_headers_btn.setEnabled(False)

        self.deselect_all_headers_btn = QPushButton("Deselect All Headers")
        self.deselect_all_headers_btn.clicked.connect(self.deselectAllHeaders)
        self.deselect_all_headers_btn.setEnabled(False)

        control_layout.addWidget(self.select_all_headers_btn)
        control_layout.addWidget(self.deselect_all_headers_btn)
        control_layout.addStretch()
        headers_layout.addLayout(control_layout)

        # Container cho các headers
        self.headers_container = QWidget()
        self.headers_layout = QVBoxLayout(self.headers_container)
        self.headers_layout.setContentsMargins(0, 0, 0, 0)
        self.headers_layout.setSpacing(5)
        self.headers_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # Scroll area để chứa headers_container
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.headers_container)
        headers_layout.addWidget(scroll_area)

        headers_group.setLayout(headers_layout)

        # Khu vực log - Điều chỉnh lại kích thước
        log_group = QGroupBox("Processing Log")
        log_layout = QVBoxLayout()
        log_layout.setContentsMargins(5, 5, 5, 5)  # Giảm margins

        self.log_edit = QTextEdit()
        self.log_edit.setReadOnly(True)
        log_layout.addWidget(self.log_edit)

        button_layout = QHBoxLayout()
        self.process_btn = QPushButton("Process Data")
        self.process_btn.clicked.connect(self.processData)
        self.process_btn.setEnabled(False)
        button_layout.addWidget(self.process_btn)

        log_layout.addLayout(button_layout)
        log_group.setLayout(log_layout)

        # Thêm Headers và Log vào container với tỉ lệ 50-50 theo chiều dọc
        rules_log_layout.addWidget(headers_group, 1)  # tỉ lệ 1
        rules_log_layout.addWidget(log_group, 1)  # tỉ lệ 1

        # Thêm container vào layout chính
        main_layout.addWidget(rules_log_container, 1)

        # Lấy thông tin từ bước 1
        self.tryLoadFromStep1()

    def tryLoadFromStep1(self):
        """Tải thông tin từ bước 1 đã lưu trong file tạm"""
        try:
            # Nếu đã có thông tin, không cần load lại
            if self.spreadsheet_id and self.sheet_title:
                self.log_edit.append(f"Using existing spreadsheet information: {self.spreadsheet_id}, {self.sheet_title}\n")
                return

            temp_dir = tempfile.gettempdir()
            temp_file = os.path.join(temp_dir, "raw_processed_dest.txt")
            if os.path.exists(temp_file):
                with open(temp_file, "r", encoding="utf-8") as f:
                    lines = f.readlines()
                    if len(lines) >= 2:
                        self.spreadsheet_id = lines[0].strip()
                        self.sheet_title = lines[1].strip()
                        self.sheet_id_label.setText(self.spreadsheet_id)
                        self.sheet_title_label.setText(self.sheet_title)
                        self.log_edit.append("Loaded spreadsheet information from Step 1\n")
        except Exception as e:
            self.log_edit.append(f"Could not load from Step 1: {str(e)}\n")

    def onHeaderRowChanged(self, index):
        """Xử lý khi người dùng thay đổi dòng header"""
        self.header_row = index + 1  # ComboBox index bắt đầu từ 0, header row bắt đầu từ 1
        # Nếu đã load headers, load lại khi thay đổi dòng header
        if self.headers:
            self.loadHeaders()

    def loadHeaders(self):
        """Tải thông tin headers từ sheet đã chọn"""
        if not self.spreadsheet_id or not self.sheet_title:
            QMessageBox.warning(self, "Missing Information", "Please specify Spreadsheet ID and Sheet Title first")
            return

        self.progress_dialog = ProgressDialog(self)
        self.progress_dialog.update_info("Loading sheet headers...")
        self.progress_dialog.show()

        QApplication.processEvents()

        try:
            # Tính dòng header (1-based)
            header_row_1based = self.header_row
            range_name = f"'{self.sheet_title}'!A{header_row_1based}:ZZ{header_row_1based}"  # Mở rộng đến cột ZZ

            request = self.gs_service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=range_name,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            request.http.timeout = 60
            result = request.execute()

            values = result.get('values', [[]])
            if values and values[0]:
                # Lọc các header không rỗng
                self.headers = [str(h) for h in values[0] if h]
                self.log_edit.append(f"Successfully loaded {len(self.headers)} headers from row {header_row_1based}\n")

                # Tạo UI cho headers (bỏ qua 3 header đầu tiên)
                self.createHeadersUI()

                # Enable các nút điều khiển
                self.select_all_headers_btn.setEnabled(True)
                self.deselect_all_headers_btn.setEnabled(True)

                # Enable nút xử lý dữ liệu
                self.process_btn.setEnabled(True)
            else:
                self.headers = []
                self.log_edit.append(f"No headers found at row {header_row_1based}\n")
        except Exception as e:
            self.log_edit.append(f"Error loading headers: {str(e)}\n")
            QMessageBox.critical(self, "Error", f"Error loading headers: {str(e)}")
        finally:
            if self.progress_dialog:
                self.progress_dialog.close()

    def createHeadersUI(self):
        """Tạo UI cho việc chọn headers và điều kiện"""
        # Xóa tất cả widgets cũ trong headers_container
        for i in reversed(range(self.headers_layout.count())):
            child = self.headers_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Reset dictionaries
        self.header_checkboxes = {}
        self.condition_checkboxes = {}

        # Bỏ qua 3 header đầu tiên
        display_headers = self.headers[3:] if len(self.headers) > 3 else self.headers

        for header in display_headers:
            # Tạo group box cho mỗi header
            header_group = QGroupBox(header)
            header_layout = QVBoxLayout()
            header_layout.setContentsMargins(5, 5, 5, 5)
            header_layout.setSpacing(3)

            # Checkbox chính cho header
            header_checkbox = QCheckBox("Select this header")
            header_checkbox.stateChanged.connect(lambda state, h=header: self.onHeaderCheckboxChanged(h, state))
            header_layout.addWidget(header_checkbox)
            self.header_checkboxes[header] = header_checkbox

            # Container cho các điều kiện
            conditions_widget = QWidget()
            conditions_layout = QGridLayout()
            conditions_layout.setContentsMargins(10, 0, 0, 0)  # Indent để thể hiện là sub-options
            conditions_layout.setSpacing(2)

            # Tạo checkbox cho từng điều kiện
            condition_checkboxes = {}
            for i, condition in enumerate(self.condition_types):
                condition_checkbox = QCheckBox(condition)
                condition_checkbox.setEnabled(False)  # Disable ban đầu
                condition_checkbox.stateChanged.connect(lambda state, h=header, c=condition: self.onConditionCheckboxChanged(h, c, state))

                # Sắp xếp thành 2 cột
                row = i // 2
                col = i % 2
                conditions_layout.addWidget(condition_checkbox, row, col)
                condition_checkboxes[condition] = condition_checkbox

            conditions_widget.setLayout(conditions_layout)
            header_layout.addWidget(conditions_widget)

            # Lưu condition checkboxes cho header này
            self.condition_checkboxes[header] = condition_checkboxes

            header_group.setLayout(header_layout)
            self.headers_layout.addWidget(header_group)

        # Thêm spacer để đẩy các elements lên trên
        self.headers_layout.addStretch(1)

    def onHeaderCheckboxChanged(self, header, state):
        """Xử lý khi checkbox header thay đổi"""
        is_checked = state == 2  # Qt.CheckState.Checked

        # Enable/disable các condition checkboxes
        if header in self.condition_checkboxes:
            for condition_checkbox in self.condition_checkboxes[header].values():
                condition_checkbox.setEnabled(is_checked)
                if not is_checked:
                    condition_checkbox.setChecked(False)

    def onConditionCheckboxChanged(self, header, condition, state):
        """Xử lý khi checkbox điều kiện thay đổi"""
        # Có thể thêm logic xử lý nếu cần
        pass

    def selectAllHeaders(self):
        """Chọn tất cả headers"""
        for header_checkbox in self.header_checkboxes.values():
            header_checkbox.setChecked(True)

    def deselectAllHeaders(self):
        """Bỏ chọn tất cả headers"""
        for header_checkbox in self.header_checkboxes.values():
            header_checkbox.setChecked(False)





    def processData(self):
        """Xử lý dữ liệu theo các headers và điều kiện đã chọn"""
        if not self.spreadsheet_id or not self.sheet_title:
            QMessageBox.warning(self, "Missing Information", "Please specify Spreadsheet ID and Sheet Title first")
            return

        # Kiểm tra xem có header nào được chọn không
        selected_headers = []
        for header, checkbox in self.header_checkboxes.items():
            if checkbox.isChecked():
                selected_headers.append(header)

        if not selected_headers:
            QMessageBox.warning(self, "No Headers Selected", "Please select at least one header to process")
            return

        # Kiểm tra xem mỗi header đã chọn có ít nhất một điều kiện được chọn không
        invalid_headers = []
        for header in selected_headers:
            if header in self.condition_checkboxes:
                has_condition = any(checkbox.isChecked() for checkbox in self.condition_checkboxes[header].values())
                if not has_condition:
                    invalid_headers.append(header)

        if invalid_headers:
            error_msg = "The following headers don't have any conditions selected:\n" + "\n".join(invalid_headers)
            QMessageBox.warning(self, "Invalid Selection", error_msg)
            return

        # Hiển thị dialog tiến trình
        self.progress_dialog = ProgressDialog(self)
        self.progress_dialog.update_info("Loading data...")
        self.progress_dialog.show()

        # Bắt đầu xử lý
        self.processSelectedHeaders()

    def processSelectedHeaders(self):
        """Xử lý tuần tự các headers và điều kiện đã chọn"""
        # Step 1: Tải dữ liệu
        self.progress_dialog.update_info("Loading data from spreadsheet...")

        try:
            # Tính range dữ liệu (bắt đầu từ sau dòng header)
            data_start_row = self.header_row + 1
            range_name = f"'{self.sheet_title}'!A{data_start_row}:ZZ"  # Mở rộng đến cột ZZ

            request = self.gs_service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=range_name,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            request.http.timeout = 60
            result = request.execute()

            values = result.get('values', [])
            if not values:
                self.progress_dialog.close()
                self.log_edit.append("No data found in the specified range\n")
                QMessageBox.warning(self, "No Data", "No data found in the specified range")
                return

            # Lấy thông tin cột từ headers
            header_range = f"'{self.sheet_title}'!A{self.header_row}:ZZ{self.header_row}"  # Mở rộng đến cột ZZ

            request = self.gs_service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=header_range,
                valueRenderOption='UNFORMATTED_VALUE'
            )
            request.http.timeout = 60
            header_result = request.execute()

            header_values = header_result.get('values', [[]])
            headers = header_values[0] if header_values else []

            # Chuẩn bị dữ liệu để xử lý
            self.log_edit.append(f"Loaded {len(values)} rows of data\n")
            self.progress_dialog.update_info(f"Processing {len(values)} rows of data...")

            # Step 2: Lặp qua từng header đã chọn và áp dụng các điều kiện
            total_changes = 0

            for header in self.header_checkboxes:
                if not self.header_checkboxes[header].isChecked():
                    continue

                # Lấy các điều kiện được chọn cho header này
                selected_conditions = []
                if header in self.condition_checkboxes:
                    for condition, checkbox in self.condition_checkboxes[header].items():
                        if checkbox.isChecked():
                            selected_conditions.append(condition)

                if not selected_conditions:
                    continue

                # Xác định chỉ số cột tương ứng với header
                if header not in headers:
                    self.log_edit.append(f"Warning: Header '{header}' not found in spreadsheet\n")
                    continue

                col_idx = headers.index(header)
                self.log_edit.append(f"Processing header '{header}' (column {col_idx + 1}) with conditions: {', '.join(selected_conditions)}\n")

                # Áp dụng từng điều kiện cho cột này
                for condition in selected_conditions:
                    self.progress_dialog.update_info(f"Applying '{condition}' to '{header}'...")
                    changes = self.applyCondition(values, [col_idx], condition)
                    total_changes += changes
                    self.log_edit.append(f"  - Applied '{condition}': {changes} changes\n")

                # Cập nhật tiến trình
                QApplication.processEvents()

            # Step 3: Cập nhật dữ liệu vào sheet
            if total_changes > 0:
                self.progress_dialog.update_info(f"Updating spreadsheet with {total_changes} changes...")

                # Cập nhật dữ liệu
                body = {'values': values}
                update_range = f"'{self.sheet_title}'!A{data_start_row}:ZZ{data_start_row + len(values) - 1}"  # Mở rộng đến cột ZZ

                request = self.gs_service.spreadsheets().values().update(
                    spreadsheetId=self.spreadsheet_id,
                    range=update_range,
                    valueInputOption='RAW',
                    body=body
                )
                request.http.timeout = 60
                result = request.execute()

                self.log_edit.append(f"Successfully updated {result.get('updatedCells')} cells in the spreadsheet\n")
            else:
                self.log_edit.append("No changes were made to the data\n")

            # Hoàn thành
            self.progress_dialog.close()
            QMessageBox.information(self, "Processing Complete", f"Processing completed with {total_changes} changes.")

            # Reset lại selections sau khi xử lý xong
            self.resetSelections()
        except Exception as e:
            if self.progress_dialog:
                self.progress_dialog.close()
            self.log_edit.append(f"Error processing data: {str(e)}\n")
            QMessageBox.critical(self, "Error", f"Error processing data: {str(e)}")

    def resetSelections(self):
        """Reset lại toàn bộ selections về trạng thái mặc định"""
        # Bỏ chọn tất cả headers
        for header_checkbox in self.header_checkboxes.values():
            header_checkbox.setChecked(False)

        # Bỏ chọn tất cả conditions
        for header_conditions in self.condition_checkboxes.values():
            for condition_checkbox in header_conditions.values():
                condition_checkbox.setChecked(False)

        self.log_edit.append("All selections have been reset to default.\n")



    def applyCondition(self, values, column_indices, condition_type):
        """Áp dụng một điều kiện xử lý vào dữ liệu"""
        changes_count = 0

        for row in values:
            for col_idx in column_indices:
                if col_idx < len(row):
                    # Đảm bảo giá trị là string trước khi xử lý
                    original_value = row[col_idx]
                    if original_value is None:
                        continue

                    if not isinstance(original_value, str):
                        original_value = str(original_value)

                    # Áp dụng điều kiện tương ứng
                    new_value = original_value

                    if condition_type == "Clear 'X' Values":
                        values_to_clear = ["x", "X", "không", "Không", "Không có", "No gift", "No", "no"]
                        single_char_to_clear = ["x", "X", "/", "-"]
                        if original_value.strip() in values_to_clear or (len(original_value.strip()) == 1 and original_value.strip() in single_char_to_clear):
                            new_value = ""

                    elif condition_type == "Format Numbers":
                        if original_value:
                            # Xóa dấu phẩy, dấu chấm và ký tự đồng VNĐ
                            temp_value = original_value
                            temp_value = temp_value.replace(",", "").replace(".", "")
                            # Xử lý các biến thể của ký tự đồng: đ, Đ, ₫
                            temp_value = temp_value.replace("đ", "").replace("Đ", "").replace("₫", "")
                            # Loại bỏ khoảng trắng thừa có thể xuất hiện sau khi xóa ký tự
                            new_value = temp_value.strip()

                    elif condition_type == "Convert 'k' to '000'":
                        if original_value:
                            # Format giá trị và/hoặc chuyển đổi 'k' thành '000'
                            cleaned_value = original_value.replace(",", "").replace(".", "")
                            if "k" in cleaned_value.lower():
                                new_value = cleaned_value.lower().replace("k", "000")
                            elif "," in original_value or "." in original_value:
                                new_value = cleaned_value

                    elif condition_type == "Fix Line Breaks":
                        if '\n' in original_value:
                            lines = original_value.split('\n')
                            non_empty_lines = [line.strip() for line in lines if line.strip()]
                            new_value = '\n'.join(non_empty_lines)

                    elif condition_type == "Format Percentages":
                        if original_value and ("%" in original_value or "phần trăm" in original_value.lower()):
                            # GIỮ NGUYÊN giá trị số phần trăm, chỉ đảm bảo định dạng đúng
                            # Ví dụ: "31%" vẫn là "31%" không chuyển thành 0.31 hay 0.31%
                            cleaned_value = original_value.replace("phần trăm", "").strip()

                            # Nếu đã có dấu % thì giữ nguyên
                            if cleaned_value.endswith("%"):
                                new_value = cleaned_value
                            else:
                                # Nếu chưa có dấu % thì thêm vào
                                new_value = cleaned_value + "%"

                            # Không còn chuyển đổi thành số thập phân nữa

                    elif condition_type == "Clear Empty Rows":
                        # Tính toán ở cấp độ dòng, không phải ô
                        pass

                    # Nếu giá trị thay đổi, cập nhật vào dữ liệu và đếm
                    if new_value != original_value:
                        row[col_idx] = new_value
                        changes_count += 1

        # Xử lý riêng cho Clear Empty Rows
        if condition_type == "Clear Empty Rows":
            original_row_count = len(values)
            # Xác định các dòng không rỗng
            non_empty_rows = []
            for row in values:
                if any(cell not in [None, ""] for cell in row):
                    non_empty_rows.append(row)

            # Cập nhật danh sách dòng
            values.clear()
            values.extend(non_empty_rows)

            # Đếm số dòng đã xóa
            changes_count = original_row_count - len(values)

        return changes_count



# Cập nhật RawDataWizard để thêm trang ProcessDataPage
class RawDataWizard(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        # Thêm thuộc tính để hỗ trợ tích hợp với main.py
        self.back_callback = None
        self.goto_other_program = None
        self.initUI()

    def initUI(self):
        self.setWindowTitle("Data Handler")
        self.resize(900, 600)

        # Tạo layout chính
        main_layout = QVBoxLayout(self)

        # Thêm thanh điều hướng ở trên cùng
        nav_layout = QHBoxLayout()

        # Nút Trở về
        self.back_btn = QPushButton("⬅️ Trở về giao diện chính")
        self.back_btn.setFixedSize(150, 40)
        self.back_btn.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        self.back_btn.clicked.connect(self.goBack)
        nav_layout.addWidget(self.back_btn, alignment=Qt.AlignmentFlag.AlignLeft)

        # Thêm spacer để đẩy các nút sang bên phải
        nav_layout.addStretch()

        # Nút Chuyển đến sử dụng QToolButton và QMenu giống các module khác
        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Create Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        self.context_button.setMenu(menu)
        nav_layout.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        # Thêm thanh điều hướng vào layout chính
        main_layout.addLayout(nav_layout)

        # Thêm widget stack
        self.stack = QStackedWidget(self)
        main_layout.addWidget(self.stack)

        # Cập nhật các trang
        self.copy_page = CopyDataPage()
        self.process_page = ProcessDataPage()

        self.stack.addWidget(self.copy_page)
        self.stack.addWidget(self.process_page)

        # Kết nối trang CopyDataPage với trang ProcessDataPage
        self.copy_page.proceed.connect(self.showProcessPage)

        # Bắt đầu với trang Copy Data
        self.stack.setCurrentWidget(self.copy_page)

    def eventFilter(self, obj, event):
        """Xử lý sự kiện cho context menu"""
        from PyQt6.QtCore import QEvent
        from PyQt6.QtGui import QHoverEvent

        if obj is self.context_button:
            if event.type() == QEvent.Type.Enter:
                # Hiệu ứng khi rê chuột vào
                self.context_button.setStyleSheet("""
                    QToolButton {
                        border: none;
                        background-color: rgba(0, 0, 0, 0.1);
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QToolButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """)
                # Tự động hiển thị menu khi rê chuột vào
                self.context_button.showMenu()
                return True
            elif event.type() == QEvent.Type.Leave:
                # Khôi phục trạng thái khi rời chuột
                self.context_button.setStyleSheet("""
                    QToolButton {
                        border: none;
                        background: transparent;
                        font-size: 12px;
                        font-weight: bold;
                    }
                    QToolButton::menu-indicator {
                        image: none;
                        width: 0px;
                    }
                """)
                return True
        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        """Xử lý sự kiện phím"""
        from PyQt6.QtCore import Qt
        if event.key() == Qt.Key.Key_Backspace or event.key() == Qt.Key.Key_Back:
            # Xử lý khi nhấn phím Back
            self.goBack()
        else:
            super().keyPressEvent(event)

    def showProcessPage(self, report):
        # Truyền thông tin từ report sang process_page trước khi chuyển trang
        if 'dest_spreadsheet_id' in report and 'dest_sheet_title' in report:
            self.process_page.spreadsheet_id = report['dest_spreadsheet_id']
            self.process_page.sheet_title = report['dest_sheet_title']
            self.process_page.sheet_id_label.setText(report['dest_spreadsheet_id'])
            self.process_page.sheet_title_label.setText(report['dest_sheet_title'])
            print(f"Đã truyền thông tin destination sang bước 2: {report['dest_spreadsheet_id']}, {report['dest_sheet_title']}")

        # Vẫn gọi tryLoadFromStep1 để đảm bảo tương thích ngược
        self.process_page.tryLoadFromStep1()
        self.stack.setCurrentWidget(self.process_page)

    def goBack(self):
        """Xử lý khi nhấn nút Trở về"""
        if self.back_callback:
            self.back_callback()

    def stop_processing(self):
        """Dừng tất cả tiến trình đang chạy"""
        try:
            # Dừng các worker thread nếu có
            if hasattr(self.copy_page, 'worker') and self.copy_page.worker:
                if isinstance(self.copy_page.worker, QThread) and self.copy_page.worker.isRunning():
                    self.copy_page.worker.terminate()
                    self.copy_page.worker.wait(1000)  # Đợi tối đa 1 giây để thread kết thúc

            # Đóng các dialog đang hiển thị
            if hasattr(self.copy_page, 'progress_dialog') and self.copy_page.progress_dialog:
                self.copy_page.progress_dialog.close()

            if hasattr(self.copy_page, 'loading_dialog') and self.copy_page.loading_dialog:
                self.copy_page.loading_dialog.close()

            # Dừng các worker thread của ProcessDataPage
            if hasattr(self.process_page, 'progress_dialog') and self.process_page.progress_dialog:
                self.process_page.progress_dialog.close()

            print("Đã dừng các tiến trình Raw Data Processing")

        except Exception as e:
            print(f"Lỗi khi dừng Raw Data Processing: {str(e)}")

def test_function():
    print("Test loading...")

if __name__ == '__main__':
    try:
        app = QApplication(sys.argv)
        wizard = RawDataWizard()
        wizard.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        traceback.print_exc()
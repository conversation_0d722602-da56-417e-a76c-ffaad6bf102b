import sys
import json
import os
import re
import unicodedata
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                            QTableWidget, QTableWidgetItem, QHeaderView, 
                            QMessageBox, QFileDialog, QGroupBox, QFormLayout,
                            QSpinBox, QCheckBox, QTabWidget, QTextEdit, 
                            QComboBox, QScrollArea, QSizePolicy, QDialog,
                            QGridLayout, QFrame, QRadioButton)
from PyQt6.QtCore import Qt, pyqtSlot, QSize
from PyQt6.QtGui import QIcon, QFont

from raw_data_core import RawDataCore

def normalize_header(text):
    """
    Chuẩn hóa tên header tiếng Việt: bỏ dấu, xóa khoảng trống, chuyển về lowercase
    """
    if not text:
        return ""
    
    # Chuyển về Unicode NFD để tách dấu
    text = unicodedata.normalize('NFD', text)
    # Loại bỏ các ký tự dấu
    text = ''.join([c for c in text if not unicodedata.combining(c)])
    # Xóa khoảng trống và chuyển về lowercase
    text = re.sub(r'\s+', '', text).lower()
    return text

def extract_sheet_id_from_url(url):
    """
    Trích xuất Spreadsheet ID từ URL Google Sheets
    """
    if not url:
        return ""
    
    # Pattern cho URL Google Sheets
    patterns = [
        r'https://docs\.google\.com/spreadsheets/d/([a-zA-Z0-9_-]+)',  # Link chia sẻ thông thường
        r'https://docs\.google\.com/spreadsheets/d/e/([a-zA-Z0-9_-]+)', # Link xuất bản
        r'([a-zA-Z0-9_-]{25,})'  # ID trực tiếp (giả định >= 25 ký tự)
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    # Nếu không tìm thấy theo pattern, trả về url gốc (có thể là ID trực tiếp)
    return url


class RawDataUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.raw_data_core = RawDataCore()
        self.init_ui()
        
    def init_ui(self):
        # Thiết lập cửa sổ chính
        self.setWindowTitle("Google Sheet Data Import")
        self.setMinimumSize(600, 500)
        
        # Widget chính
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        self.setCentralWidget(central_widget)
        
        # Tạo các tab
        tabs = QTabWidget()
        main_layout.addWidget(tabs)
        
        # Tab 1: Import dữ liệu
        import_tab = QWidget()
        import_layout = QVBoxLayout(import_tab)
        import_layout.setContentsMargins(5, 5, 5, 5)
        import_layout.setSpacing(10)
        tabs.addTab(import_tab, "Import Dữ Liệu")
        
        # Tab 2: Xử lý dữ liệu
        process_tab = QWidget()
        process_layout = QVBoxLayout(process_tab)
        process_layout.setContentsMargins(5, 5, 5, 5)
        process_layout.setSpacing(10)
        tabs.addTab(process_tab, "Xử Lý Dữ Liệu")
        
        # Tab 3: Nhật ký
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        log_layout.setContentsMargins(5, 5, 5, 5)
        tabs.addTab(log_tab, "Nhật Ký")
        
        # --- Nội dung tab Import dữ liệu ---
        
        # Thêm RadioButton cho chế độ Import ở trên cùng
        mode_layout = QHBoxLayout()
        mode_label = QLabel("Chế độ Import:")
        mode_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        mode_layout.addWidget(mode_label)
        
        self.copy_mode_radio = QRadioButton("Tạo Mới")
        self.copy_mode_radio.setChecked(True)  # Mặc định là chế độ Copy
        self.append_mode_radio = QRadioButton("Bổ Sung")
        mode_layout.addWidget(self.copy_mode_radio)
        mode_layout.addWidget(self.append_mode_radio)
        mode_layout.addStretch(1)
        
        import_layout.addLayout(mode_layout)
        
        # Thêm tooltip giải thích về chế độ
        tooltip_text = "Tạo Mới: Tạo mới hoàn toàn dữ liệu từ sheet nguồn.\nBổ Sung: Bổ sung dữ liệu từ dòng 'More' trong sheet nguồn vào cuối sheet đích."
        self.copy_mode_radio.setToolTip(tooltip_text)
        self.append_mode_radio.setToolTip(tooltip_text)
        
        # Phần sheet nguồn - GroupBox cho phần nhập URL
        source_input_group = QGroupBox("Sheet Nguồn")
        source_input_layout = QVBoxLayout(source_input_group)
        source_input_layout.setContentsMargins(8, 12, 8, 8)
        source_input_layout.setSpacing(8)
        
        # Phần nhập URL/ID nguồn
        url_input_layout = QHBoxLayout()
        self.source_url_input = QLineEdit()
        self.source_url_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet nguồn")
        self.source_url_input.textChanged.connect(self.on_source_url_changed)
        url_input_layout.addWidget(self.source_url_input, 1)
        
        self.load_sheets_btn = QPushButton("Tải Sheet")
        self.load_sheets_btn.clicked.connect(self.load_source_sheets)
        url_input_layout.addWidget(self.load_sheets_btn)
        
        source_input_layout.addLayout(url_input_layout)
        import_layout.addWidget(source_input_group)
        
        # Phần danh sách sheet - GroupBox cho grid 3x3
        self.sheet_list_group = QGroupBox("Danh sách Sheet Nguồn")
        self.sheet_list_group.setMinimumHeight(250)  # Đặt chiều cao tối thiểu lớn hơn
        self.sheet_list_group.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
        sheet_list_layout = QVBoxLayout(self.sheet_list_group)
        sheet_list_layout.setContentsMargins(8, 12, 8, 8)
        
        # Tạo grid layout 3x3 cố định
        self.sheet_grid_layout = QGridLayout()
        self.sheet_grid_layout.setHorizontalSpacing(10)
        self.sheet_grid_layout.setVerticalSpacing(5)
        sheet_list_layout.addLayout(self.sheet_grid_layout)
        sheet_list_layout.addStretch(1)  # Thêm stretch để các checkbox luôn nằm ở phía trên
        
        import_layout.addWidget(self.sheet_list_group)
        
        # Phần Spreadsheet đích và sheet đích
        target_group = QGroupBox("Spreadsheet Đích")
        target_layout = QFormLayout(target_group)
        target_layout.setContentsMargins(8, 12, 8, 8)
        target_layout.setSpacing(8)
        import_layout.addWidget(target_group)
        
        # URL/ID đích
        target_id_layout = QHBoxLayout()
        self.target_id_input = QLineEdit()
        self.target_id_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet đích (để trống = dùng Spreadsheet nguồn)")
        self.target_id_input.textChanged.connect(self.on_target_url_changed)
        target_id_layout.addWidget(self.target_id_input)
        
        # Nút để tải danh sách sheet
        self.load_target_sheets_btn = QPushButton("Tải")
        self.load_target_sheets_btn.clicked.connect(self.load_target_sheets)
        self.load_target_sheets_btn.setMaximumWidth(60)
        target_id_layout.addWidget(self.load_target_sheets_btn)
        
        target_layout.addRow("URL/ID đích:", target_id_layout)
        
        # Thêm combo box để chọn sheet đích
        self.target_sheet_combo = QComboBox()
        self.target_sheet_combo.setEditable(True)  # Cho phép nhập tên mới
        self.target_sheet_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        target_layout.addRow("Sheet đích:", self.target_sheet_combo)
        
        # Thêm checkbox để tự động xử lý dữ liệu sau khi import
        self.auto_process_checkbox = QCheckBox("Tự động xử lý dữ liệu sau khi import (Bước 2)")
        self.auto_process_checkbox.setChecked(True)  # Mặc định là đã chọn
        import_layout.addWidget(self.auto_process_checkbox)
        
        # Nút bắt đầu import
        import_layout.addStretch(1)
        self.import_btn = QPushButton("Bắt Đầu Import Dữ Liệu")
        self.import_btn.setMinimumHeight(36)
        import_layout.addWidget(self.import_btn)
        self.import_btn.clicked.connect(self.start_import)

        # --- Nội dung tab Xử Lý Dữ Liệu ---
        process_intro_label = QLabel("Xử lý dữ liệu đã import để chuẩn hóa các giá trị")
        process_layout.addWidget(process_intro_label)
        
        # Phần nhập thông tin spreadsheet cần xử lý
        process_spreadsheet_group = QGroupBox("Thông tin Spreadsheet")
        process_spreadsheet_layout = QFormLayout(process_spreadsheet_group)
        
        # URL/ID của spreadsheet cần xử lý
        self.process_spreadsheet_input = QLineEdit()
        self.process_spreadsheet_input.setPlaceholderText("Nhập URL hoặc ID Spreadsheet cần xử lý")
        process_spreadsheet_layout.addRow("URL/ID:", self.process_spreadsheet_input)
        
        # Dropdown chọn sheet cần xử lý
        self.process_sheet_combo = QComboBox()
        self.process_sheet_combo.setEditable(True)
        self.process_sheet_combo.setInsertPolicy(QComboBox.InsertPolicy.NoInsert)
        process_spreadsheet_layout.addRow("Sheet:", self.process_sheet_combo)
        
        # Nút tải danh sách sheet
        load_process_sheets_layout = QHBoxLayout()
        self.load_process_sheets_btn = QPushButton("Tải danh sách sheet")
        self.load_process_sheets_btn.clicked.connect(self.load_process_sheets)
        load_process_sheets_layout.addWidget(self.load_process_sheets_btn)
        load_process_sheets_layout.addStretch(1)
        process_spreadsheet_layout.addRow("", load_process_sheets_layout)
        
        process_layout.addWidget(process_spreadsheet_group)
        
        # Các quy tắc xử lý dữ liệu
        process_rules_group = QGroupBox("Quy tắc xử lý")
        process_rules_layout = QVBoxLayout(process_rules_group)
        
        # Thông tin về các quy tắc xử lý tự động
        rules_info = """
        Dữ liệu sẽ được xử lý theo các quy tắc sau:
        
        1. Xử lý các giá trị X/Không: Loại bỏ các giá trị "X", "Không", "Không có" trong cột trạng thái 
        2. Định dạng số liệu: Loại bỏ dấu phẩy, chấm và ký tự đồng
        3. Chuyển đổi 'k' thành '000': Chuyển "50k" thành "50000"
        4. Xử lý xuống dòng: Loại bỏ các dòng trống trong văn bản
        5. Định dạng phần trăm: Chuẩn hóa các giá trị phần trăm
        6. Xóa các dòng trống: Loại bỏ các dòng không có dữ liệu
        """
        rules_info_label = QLabel(rules_info)
        rules_info_label.setWordWrap(True)
        process_rules_layout.addWidget(rules_info_label)
        
        process_layout.addWidget(process_rules_group)
        
        # Nút bắt đầu xử lý
        process_layout.addStretch(1)
        self.process_data_btn = QPushButton("Bắt Đầu Xử Lý Dữ Liệu")
        self.process_data_btn.setMinimumHeight(36)
        self.process_data_btn.clicked.connect(self.start_processing)
        process_layout.addWidget(self.process_data_btn)
        
        # --- Nội dung tab Nhật ký ---
        # Text edit để hiển thị nhật ký
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # Hiển thị thông báo khởi động
        self.log("Ứng dụng đã khởi động")
    
    def on_source_url_changed(self, text):
        """Tự động parse ID từ URL của spreadsheet nguồn"""
        if not text.strip():
            return
            
        sheet_id = extract_sheet_id_from_url(text)
        # Nếu có ID được parse ra và khác với text đã nhập
        if sheet_id and sheet_id != text:
            # Cập nhật text trong input box
            self.source_url_input.blockSignals(True)  # Tạm dừng signals để tránh đệ quy
            self.source_url_input.setText(sheet_id)
            self.source_url_input.blockSignals(False)
            self.source_spreadsheet_id = sheet_id
            self.log(f"Đã parse ID nguồn: {sheet_id} từ URL")
        elif sheet_id:
            # Nếu text đã nhập là ID hoặc đã được parse trước đó
            self.source_spreadsheet_id = sheet_id
    
    def load_source_sheets(self):
        """Tải danh sách sheet từ Spreadsheet nguồn và hiển thị dạng grid layout 3x3"""
        if not hasattr(self, 'source_spreadsheet_id') or not self.source_spreadsheet_id:
            url = self.source_url_input.text().strip()
            self.source_spreadsheet_id = extract_sheet_id_from_url(url)
            
        if not self.source_spreadsheet_id:
            QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet nguồn hợp lệ")
            return
            
        try:
            # Xóa checkboxes cũ nếu có
            self.clear_sheet_grid()
            
            # Lấy danh sách sheet
            self.log(f"Đang tải sheets từ Spreadsheet ID: {self.source_spreadsheet_id}")
            sheet_list = self.raw_data_core.get_sheet_list(self.source_spreadsheet_id)
            
            if sheet_list:
                # Tạo grid các checkbox cho việc chọn sheet
                cols = 3  # Số cột
                rows = (len(sheet_list) + cols - 1) // cols  # Số dòng cần thiết (làm tròn lên)
                self.sheet_checkboxes = []
                
                # Đặt kích thước cố định cho grid
                for row in range(rows):
                    # Đảm bảo grid có chiều cao đồng đều
                    self.sheet_grid_layout.setRowMinimumHeight(row, 30)
                
                # Thêm checkbox cho mỗi sheet
                for i, sheet_name in enumerate(sheet_list):
                    row = i // cols
                    col = i % cols
                    
                    checkbox = QCheckBox(sheet_name)
                    self.sheet_checkboxes.append(checkbox)
                    self.sheet_grid_layout.addWidget(checkbox, row, col)
                
                self.log(f"Đã tải {len(sheet_list)} sheets")
                
                # Nếu có sheet đích trống, tự động điền ID nguồn vào đích
                if not self.target_id_input.text().strip():
                    self.target_id_input.setText(self.source_spreadsheet_id)
                    self.load_target_sheets()
            else:
                self.log("Không tìm thấy sheet nào trong spreadsheet này")
                
        except Exception as e:
            self.log(f"Lỗi khi tải sheets: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể tải danh sách sheet: {str(e)}")
    
    def clear_sheet_grid(self):
        """Xóa tất cả checkbox cũ trong grid"""
        if hasattr(self, 'sheet_checkboxes'):
            for checkbox in self.sheet_checkboxes:
                checkbox.setParent(None)
            self.sheet_checkboxes = []
            
        # Xóa tất cả widget trong grid layout
        while self.sheet_grid_layout.count():
            item = self.sheet_grid_layout.takeAt(0)
            widget = item.widget()
            if widget is not None:
                widget.deleteLater()
    
    def get_selected_sheets(self):
        """Lấy danh sách các sheet đã chọn"""
        selected_sheets = []
        
        if hasattr(self, 'sheet_checkboxes') and hasattr(self, 'source_spreadsheet_id'):
            for checkbox in self.sheet_checkboxes:
                if checkbox.isChecked():
                    selected_sheets.append({
                        'spreadsheet_id': self.source_spreadsheet_id,
                        'sheet_name': checkbox.text(),
                    })
                
        return selected_sheets
        
    def on_target_url_changed(self, text):
        """Tự động parse ID từ URL của spreadsheet đích"""
        if not text.strip():
            return
            
        sheet_id = extract_sheet_id_from_url(text)
        # Nếu có ID được parse ra và khác với text đã nhập
        if sheet_id and sheet_id != text:
            # Cập nhật text trong input box
            self.target_id_input.blockSignals(True)  # Tạm dừng signals để tránh đệ quy
            self.target_id_input.setText(sheet_id)
            self.target_id_input.blockSignals(False)
            self.log(f"Đã parse ID đích: {sheet_id} từ URL")
    
    def load_target_sheets(self):
        """Tải danh sách sheet từ spreadsheet đích"""
        target_url = self.target_id_input.text().strip()
        if not target_url:
            # Nếu trống, thử sử dụng ID nguồn
            if hasattr(self, 'source_spreadsheet_id'):
                target_url = self.source_spreadsheet_id
                self.target_id_input.setText(target_url)
                self.log(f"Sử dụng Spreadsheet nguồn làm đích: {target_url}")
            else:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet đích, hoặc tải sheet nguồn trước")
                return
        
        # Parse spreadsheet ID từ URL nếu cần
        target_id = extract_sheet_id_from_url(target_url)
            
        try:
            self.log(f"Đang tải danh sách sheet từ Spreadsheet ID đích: {target_id}")
            
            # Lấy danh sách sheet từ raw_data_core
            sheet_list = self.raw_data_core.get_sheet_list(target_id)
            
            # Xóa danh sách sheet hiện tại
            self.target_sheet_combo.clear()
            
            # Thêm một item trống để không chọn sheet nào mặc định
            self.target_sheet_combo.addItem("")
            
            # Thêm vào danh sách mới
            if sheet_list:
                for sheet_name in sheet_list:
                    self.target_sheet_combo.addItem(sheet_name)
                    
                # Nếu có sheet tên "Pool Deal", chọn nó
                pool_deal_index = self.target_sheet_combo.findText("Pool Deal")
                if pool_deal_index >= 0:
                    self.target_sheet_combo.setCurrentIndex(pool_deal_index)
                    self.log(f"Đã tự động chọn sheet 'Pool Deal'")
                else:
                    # Mặc định không chọn sheet nào, để trống
                    self.target_sheet_combo.setCurrentIndex(0)
                    
                self.log(f"Đã tải {len(sheet_list)} sheet từ spreadsheet đích")
            else:
                self.log("Không tìm thấy sheet nào trong spreadsheet đích này")
                
        except Exception as e:
            self.log(f"Lỗi khi tải danh sách sheet đích: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể tải danh sách sheet đích: {str(e)}")
        
    def log(self, message):
        """Thêm thông báo vào nhật ký"""
        self.log_text.append(message)
        
    def load_process_sheets(self):
        """Tải danh sách sheet từ spreadsheet cần xử lý"""
        spreadsheet_url = self.process_spreadsheet_input.text().strip()
        if not spreadsheet_url:
            # Nếu trống, thử sử dụng ID đã nhập ở tab Import
            if hasattr(self, 'target_id_input') and self.target_id_input.text().strip():
                spreadsheet_url = self.target_id_input.text().strip()
                self.process_spreadsheet_input.setText(spreadsheet_url)
                self.log(f"Sử dụng Spreadsheet đích từ tab Import: {spreadsheet_url}")
            else:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet cần xử lý")
                return
        
        # Parse spreadsheet ID từ URL
        spreadsheet_id = extract_sheet_id_from_url(spreadsheet_url)
            
        try:
            self.log(f"Đang tải danh sách sheet từ Spreadsheet ID: {spreadsheet_id}")
            
            # Lấy danh sách sheet
            sheet_list = self.raw_data_core.get_sheet_list(spreadsheet_id)
            
            # Xóa danh sách sheet hiện tại
            self.process_sheet_combo.clear()
            
            # Thêm vào danh sách mới
            if sheet_list:
                for sheet_name in sheet_list:
                    self.process_sheet_combo.addItem(sheet_name)
                    
                # Nếu có sheet tên "Pool Deal", chọn nó
                pool_deal_index = self.process_sheet_combo.findText("Pool Deal")
                if pool_deal_index >= 0:
                    self.process_sheet_combo.setCurrentIndex(pool_deal_index)
                    self.log(f"Đã tự động chọn sheet 'Pool Deal'")
                else:
                    # Chọn sheet đầu tiên
                    self.process_sheet_combo.setCurrentIndex(0)
                    
                self.log(f"Đã tải {len(sheet_list)} sheet từ spreadsheet")
            else:
                self.log("Không tìm thấy sheet nào trong spreadsheet này")
                
        except Exception as e:
            self.log(f"Lỗi khi tải danh sách sheet: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Không thể tải danh sách sheet: {str(e)}")
    
    def start_processing(self):
        """Bắt đầu xử lý dữ liệu (Bước 2)"""
        try:
            # Lấy Spreadsheet ID
            spreadsheet_url = self.process_spreadsheet_input.text().strip()
            if not spreadsheet_url:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet cần xử lý")
                return
            
            # Parse ID từ URL
            spreadsheet_id = extract_sheet_id_from_url(spreadsheet_url)
                
            # Lấy tên sheet
            sheet_name = self.process_sheet_combo.currentText().strip()
            if not sheet_name:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng chọn sheet cần xử lý")
                return
                
            # Log thông tin
            self.log(f"Bắt đầu xử lý dữ liệu trên sheet '{sheet_name}' trong Spreadsheet {spreadsheet_id}")
            
            # Kích hoạt quá trình xử lý dữ liệu
            result = self.raw_data_core.process_imported_data(spreadsheet_id, sheet_name)
            
            if result:
                self.log("Xử lý dữ liệu thành công!")
                QMessageBox.information(self, "Thành Công", "Đã xử lý dữ liệu thành công!")
            else:
                self.log("Xử lý dữ liệu thất bại!")
                QMessageBox.critical(self, "Lỗi", "Không thể xử lý dữ liệu. Xem nhật ký để biết thêm chi tiết.")
                
        except Exception as e:
            self.log(f"Lỗi khi xử lý dữ liệu: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi: {str(e)}")
    
    @pyqtSlot()
    def start_import(self):
        """Bắt đầu quá trình import dữ liệu"""
        try:
            # Lấy danh sách sheet nguồn đã chọn
            source_data = self.get_selected_sheets()
            if not source_data:
                QMessageBox.warning(self, "Cảnh Báo", "Vui lòng chọn ít nhất một sheet nguồn")
                return
            
            # Lấy Spreadsheet ID đích
            target_url = self.target_id_input.text().strip()
            if not target_url:
                # Nếu trống, sử dụng ID nguồn
                if hasattr(self, 'source_spreadsheet_id'):
                    target_url = self.source_spreadsheet_id
                    self.log(f"Sử dụng Spreadsheet nguồn làm đích: {target_url}")
                else:
                    QMessageBox.warning(self, "Cảnh Báo", "Vui lòng nhập URL hoặc ID Spreadsheet đích, hoặc tải sheet nguồn trước")
                    return
            
            # Parse ID từ URL
            target_spreadsheet_id = extract_sheet_id_from_url(target_url)
                
            # Lấy tên sheet đích (nếu có)
            target_sheet = self.target_sheet_combo.currentText().strip()
            
            # Nếu không chọn sheet nào hoặc để trống, sử dụng sheet Pool Deal mặc định
            if not target_sheet:
                target_sheet = "Pool Deal"
                self.log(f"Không có sheet đích được chọn. Sẽ tạo sheet '{target_sheet}' từ template.")
                
            # Log thông tin
            self.log(f"Bắt đầu import dữ liệu vào Sheet '{target_sheet}' trong Spreadsheet {target_spreadsheet_id}")
            
            # Xác định chế độ import từ RadioButton
            mode = "append" if self.append_mode_radio.isChecked() else "copy"
            self.log(f"Chế độ import: {mode}")
            
            # Kiểm tra xem có tự động xử lý dữ liệu không
            auto_process = self.auto_process_checkbox.isChecked()
            
            # Nếu không tự động xử lý, ghi đè phương thức process_imported_data tạm thời
            if not auto_process:
                # Lưu phương thức gốc
                original_process_method = self.raw_data_core.process_imported_data
                
                # Tạo phương thức ghi đè tạm thời
                def temp_process_method(spreadsheet_id, sheet_title):
                    self.log("Bỏ qua bước 2: Xử lý dữ liệu (đã tắt tự động xử lý)")
                    return True
                
                # Ghi đè phương thức
                self.raw_data_core.process_imported_data = temp_process_method
            
            # Kích hoạt quá trình import - để hàm process_data_import tự tạo mapping từ sheet đích
            result = self.raw_data_core.process_data_import(
                target_spreadsheet_id, 
                source_data, 
                target_sheet, 
                column_mapping=None,  # Để None để tự tạo mapping từ sheet đích thực tế
                normalize_header_func=normalize_header,
                mode=mode
            )
            
            # Khôi phục phương thức gốc nếu đã ghi đè
            if not auto_process:
                self.raw_data_core.process_imported_data = original_process_method
            
            if result:
                completion_message = "Đã import dữ liệu thành công!"
                if auto_process:
                    completion_message += " Dữ liệu đã được xử lý tự động (Bước 2)."
                
                self.log(completion_message)
                QMessageBox.information(self, "Thành Công", completion_message)
                
                # Cập nhật thông tin cho tab Xử lý dữ liệu
                self.process_spreadsheet_input.setText(target_spreadsheet_id)
                self.load_process_sheets()
                
                # Tìm và chọn sheet đích trong combo box
                sheet_index = self.process_sheet_combo.findText(target_sheet)
                if sheet_index >= 0:
                    self.process_sheet_combo.setCurrentIndex(sheet_index)
            else:
                self.log("Import dữ liệu thất bại!")
                QMessageBox.critical(self, "Lỗi", "Không thể import dữ liệu. Xem nhật ký để biết thêm chi tiết.")
                
        except Exception as e:
            self.log(f"Lỗi khi import dữ liệu: {str(e)}")
            QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi: {str(e)}")

# Hàm chính để chạy ứng dụng
def main():
    app = QApplication(sys.argv)
    window = RawDataUI()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main() 
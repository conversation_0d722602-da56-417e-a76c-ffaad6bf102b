#!/usr/bin/env python3
"""
Test file để kiểm tra UI mới của Step 2
"""

import sys
import os

# Thêm thư mục hiện tại vào Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication
from data_ui import RawDataWizard

def main():
    """Hàm chính để test UI mới"""
    try:
        app = QApplication(sys.argv)

        # Tạo wizard
        wizard = RawDataWizard()
        wizard.show()

        # Chuyển trực tiếp đến ProcessDataPage để test
        wizard.stack.setCurrentWidget(wizard.process_page)

        # Thiết lập một số dữ liệu test
        wizard.process_page.spreadsheet_id = "test_spreadsheet_id"
        wizard.process_page.sheet_title = "test_sheet"
        wizard.process_page.sheet_id_label.setText("test_spreadsheet_id")
        wizard.process_page.sheet_title_label.setText("test_sheet")

        # Thiết lập headers test
        test_headers = [
            "STT", "Mã Deal", "Tên Deal",  # 3 header đầu sẽ bị bỏ qua
            "Giá gốc", "Giá bán", "Mô tả sản phẩm",
            "Link sản phẩm", "Số lượng", "Trạng thái",
            "Ghi chú", "Ngày tạo", "Người tạo"
        ]
        wizard.process_page.headers = test_headers

        # Enable các nút và rules
        wizard.process_page.add_rule_btn.setEnabled(True)

        # Enable rule mặc định nếu tồn tại
        if wizard.process_page.processing_rules:
            for rule_widget in wizard.process_page.processing_rules:
                rule_widget.setEnabled(True)

        wizard.process_page.process_btn.setEnabled(True)

        wizard.process_page.log_edit.append("Test UI loaded successfully!\n")
        wizard.process_page.log_edit.append(f"Loaded {len(test_headers)} test headers\n")
        wizard.process_page.log_edit.append("You can now select headers and conditions to test the new UI\n")

        sys.exit(app.exec())

    except Exception as e:
        print(f"Error running test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

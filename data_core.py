import re, json, time, base64, logging, tempfile, os
from typing import List, Dict, Tuple, Any, Set, Optional
from datetime import datetime

from gsheet_manager import GoogleSheetManager

import gspread
from google.oauth2.service_account import Credentials
from google.oauth2 import service_account

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("raw_data_core")
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)

BATCH_SIZE = 150  # Kích thước batch để tương thích với external_update.py
MAX_ROWS_PER_BATCH = 100  # Số dòng tối đa trong mỗi batch API
TEMPLATE_SPREADSHEET_ID = "1zrpyulMDZGcB7wAH9NXrRMdjOLYw4h3Tsf8n7EvS5oU"
TEMPLATE_SHEET_TITLE = "Pool Deal"

BASE64_CREDENTIALS = ("************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")

def extract_spreadsheet_id(text: str) -> str:
    text = text.strip()
    if "spreadsheets/d/" in text:
        match = re.search(r'/spreadsheets/d/([a-zA-Z0-9-_]+)', text)
        if match:
            return match.group(1)
    return text

def batch_data(records: List[Any], batch_size: int = BATCH_SIZE) -> List[List[Any]]:
    return [records[i:i + batch_size] for i in range(0, len(records), batch_size)]

def clear_conditional_format_rules(gs_service, spreadsheet_id: str, sheet_id: int) -> None:
    try:
        # Lấy toàn bộ thông tin về bảng tính thay vì chỉ yêu cầu conditionalFormatRules
        spreadsheet = gs_service.spreadsheets().get(
            spreadsheetId=spreadsheet_id
        ).execute()
        
        # Tìm sheet tương ứng với sheet_id
        target_sheet = None
        for sheet in spreadsheet.get('sheets', []):
            if sheet.get('properties', {}).get('sheetId') == sheet_id:
                target_sheet = sheet
                break
        
        if not target_sheet:
            logger.info(f"Không tìm thấy sheet với ID {sheet_id} trong spreadsheet {spreadsheet_id}")
            return
        
        # Lấy các điều kiện định dạng (nếu có)
        conditional_formats = target_sheet.get('conditionalFormats', [])
        
        if conditional_formats:
            # Tạo các yêu cầu xóa (luôn xóa rule đầu tiên vì index sẽ tự thay đổi sau mỗi lần xóa)
            requests = []
            for _ in range(len(conditional_formats)):
                requests.append({
                    "deleteConditionalFormatRule": {
                        "sheetId": sheet_id,
                        "index": 0
                    }
                })
            
            # Thực hiện yêu cầu xóa
        if requests:
            body = {"requests": requests}
            gs_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id, 
                body=body
            ).execute()
            logger.info(f"Đã xóa {len(requests)} luật định dạng có điều kiện cho sheet {sheet_id}")
        else:
            logger.info(f"Không có luật định dạng có điều kiện nào cần xóa cho sheet {sheet_id}")
    except Exception as e:
        logger.error(f"Lỗi khi xóa luật định dạng có điều kiện: {e}")

def copy_sheet_template(gs_service, source_spreadsheet_id: str, source_sheet_id: int, dest_spreadsheet_id: str, dest_sheet_title: str) -> Tuple[int, str]:
    max_retries = 3
    retry_delay = 2
    for attempt in range(max_retries):
        try:
            spreadsheet = gs_service.spreadsheets().get(spreadsheetId=dest_spreadsheet_id).execute()
            existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet.get('sheets', [])]
            final_sheet_title = dest_sheet_title
            if (final_sheet_title in existing_sheets):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                final_sheet_title = f"{dest_sheet_title}_{timestamp}"
            template_spreadsheet = gs_service.spreadsheets().get(spreadsheetId=source_spreadsheet_id).execute()
            template_sheets = template_spreadsheet.get('sheets', [])
            template_sheet = next((s for s in template_sheets if s["properties"]["title"] == TEMPLATE_SHEET_TITLE), None)
            if not template_sheet:
                return None, None
            current_sheet_id = template_sheet["properties"]["sheetId"]
            if current_sheet_id != source_sheet_id:
                source_sheet_id = current_sheet_id
            body = {"destinationSpreadsheetId": dest_spreadsheet_id}
            response = gs_service.spreadsheets().sheets().copyTo(
                spreadsheetId=source_spreadsheet_id,
                sheetId=source_sheet_id,
                body=body
            ).execute()
            new_sheet_id = response.get("sheetId")
            if not new_sheet_id:
                raise Exception("No new sheet ID returned")
            requests = [{"updateSheetProperties": {"properties": {"sheetId": new_sheet_id, "title": final_sheet_title}, "fields": "title"}}]
            body = {"requests": requests}
            gs_service.spreadsheets().batchUpdate(spreadsheetId=dest_spreadsheet_id, body=body).execute()
            
            clear_conditional_format_rules(gs_service, dest_spreadsheet_id, new_sheet_id)
            
            return new_sheet_id, final_sheet_title
        except Exception as e:
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None, None

def extend_rows_with_formatting(gs_service, spreadsheet_id: str, sheet_id: int, sheet_title: str, start_row: int, num_rows: int, num_columns: int) -> None:
    try:
        requests = [{"appendDimension": {"sheetId": sheet_id, "dimension": "ROWS", "length": num_rows}}]
        body = {"requests": requests}
        gs_service.spreadsheets().batchUpdate(spreadsheetId=spreadsheet_id, body=body).execute()
        requests = []
        source_range = {"sheetId": sheet_id, "startRowIndex": 3, "endRowIndex": 4, "startColumnIndex": 0, "endColumnIndex": num_columns}
        dest_range = {"sheetId": sheet_id, "startRowIndex": start_row - 1, "endRowIndex": start_row - 1 + num_rows, "startColumnIndex": 0, "endColumnIndex": num_columns}
        requests.append({"copyPaste": {"source": source_range, "destination": dest_range, "pasteType": "PASTE_FORMAT"}})
        body = {"requests": requests}
        gs_service.spreadsheets().batchUpdate(spreadsheetId=spreadsheet_id, body=body).execute()
    except Exception as e:
        logger.error(f"Error extending rows with formatting: {e}")

def detect_header_row_custom(row_data: List[Dict]) -> int:
    for i, row in enumerate(row_data):
        if "values" in row:
            for cell in row["values"]:
                if cell.get("formattedValue", "").strip() == "KOL Pick":
                    return i
    return -1

def get_cell_info(cell: Dict) -> Dict:
    return {"value": cell.get("formattedValue", ""), "format": cell.get("userEnteredFormat", {}), "dataValidation": cell.get("dataValidation", None)}

def get_header_mapping(gs_service, spreadsheet_id: str, sheet: Dict, header_cache: Dict) -> Tuple[int, List[str]]:
    sheet_id = sheet["properties"]["sheetId"]
    key = f"{spreadsheet_id}_{sheet_id}"
    if key in header_cache:
        return header_cache[key]
    try:
        result = gs_service.spreadsheets().get(
            spreadsheetId=spreadsheet_id,
            ranges=[f"'{sheet['properties']['title']}'!A1:AH"],
            includeGridData=True
        ).execute()
        sheets_data = result.get("sheets", [])
        if not sheets_data:
            return None
        grid = sheets_data[0].get("data", [])
        if not grid:
            return None
        row_data = grid[0].get("rowData", [])
        if not row_data:
            return None
        header_index = detect_header_row_custom(row_data)
        if header_index == -1:
            return None
        header_row = row_data[header_index].get("values", [])
        headers = []
        for idx, cell in enumerate(header_row):
            if idx < 34:
                value = get_cell_info(cell)["value"].replace('\n', ' ').strip()
                headers.append(value if value else f"Column_{idx+1}")
        header_cache[key] = (header_index, headers)
        return (header_index, headers)
    except Exception as e:
        logger.error(f"Error in get_header_mapping for sheet {sheet['properties']['title']}: {e}")
        return None

def auto_map_columns(source_headers: List[str], dest_headers: List[str]) -> Dict[str, str]:
    mapping = {}
    
    # Debug và ghi log tất cả headers để xác định vấn đề
    logger.info("=== SOURCE HEADERS ===")
    for idx, header in enumerate(source_headers):
        logger.info(f"Source[{idx}]: {header}")
    
    logger.info("=== DESTINATION HEADERS ===")
    for idx, header in enumerate(dest_headers):
        logger.info(f"Dest[{idx}]: {header}")

    # Cơ chế đặc biệt để xử lý 2 cột dễ nhầm lẫn
    price_col_indexes = []  # Chỉ mục các cột về "giá sau cùng" 
    teaser_col_indexes = [] # Chỉ mục các cột về "teaser/hình ảnh"
    
    # Tìm các cột đặc biệt này trong dữ liệu nguồn
    for idx, header in enumerate(source_headers):
        header_lower = header.lower()
        # Bỏ qua cột A, B, C
        if idx in [0, 1, 2]:
            continue
        
        # Tìm cột "giá sau cùng" với điều kiện chính xác hơn
        if ('công bố giá' in header_lower or 'giá sau cùng' in header_lower) and ('cho phép' in header_lower or 'được quyền' in header_lower):
            price_col_indexes.append(idx)
            logger.info(f"Found price column in source at index {idx}: {header}")
        
        # Tìm cột "teaser" với điều kiện chính xác hơn và loại trừ cột không liên quan
        elif (('teaser video' in header_lower or 'teaser post' in header_lower) or 
              ('hình ảnh' in header_lower and 'được sử dụng' in header_lower)) and 'link hình' not in header_lower:
            teaser_col_indexes.append(idx)
            logger.info(f"Found teaser column in source at index {idx}: {header}")
    
    # Tìm các cột đặc biệt này trong dữ liệu đích
    dest_price_col_indexes = []
    dest_teaser_col_indexes = []
    
    for idx, header in enumerate(dest_headers):
        header_lower = header.lower()
        # Tìm cột "giá sau cùng" với điều kiện chính xác hơn
        if ('công bố giá' in header_lower or 'giá sau cùng' in header_lower) and ('cho phép' in header_lower or 'được quyền' in header_lower):
            dest_price_col_indexes.append(idx)
            logger.info(f"Found price column in dest at index {idx}: {header}")
        
        # Tìm cột "teaser" với điều kiện chính xác hơn
        elif ('teaser video' in header_lower or 'teaser post' in header_lower) or ('hình ảnh' in header_lower and 'được sử dụng' in header_lower) and 'link hình' not in header_lower:
            dest_teaser_col_indexes.append(idx)
            logger.info(f"Found teaser column in dest at index {idx}: {header}")
    
    # Xử lý quan hệ liên quan giữa cột "giá sau cùng" và "teaser"
    # Nếu cả 2 cột đều được tìm thấy ở cả nguồn và đích
    if price_col_indexes and teaser_col_indexes and dest_price_col_indexes and dest_teaser_col_indexes:
        # Map cột "giá sau cùng"
        src_price_idx = price_col_indexes[0]  # Lấy cột đầu tiên tìm thấy
        dest_price_idx = dest_price_col_indexes[0]
        mapping[source_headers[src_price_idx]] = dest_headers[dest_price_idx]
        logger.info(f"SPECIAL MAP: Price column '{source_headers[src_price_idx]}' -> '{dest_headers[dest_price_idx]}'")
        
        # Map cột "teaser" 
        src_teaser_idx = teaser_col_indexes[0]
        dest_teaser_idx = dest_teaser_col_indexes[0]
        mapping[source_headers[src_teaser_idx]] = dest_headers[dest_teaser_idx]
        logger.info(f"SPECIAL MAP: Teaser column '{source_headers[src_teaser_idx]}' -> '{dest_headers[dest_teaser_idx]}'")
        
        # Kiểm tra quan hệ vị trí để xác minh
        if src_teaser_idx > src_price_idx and dest_teaser_idx > dest_price_idx:
            logger.info("Position relationship verified: Teaser column is after Price column in both source and dest")
        elif src_teaser_idx < src_price_idx and dest_teaser_idx < dest_price_idx:
            logger.info("Position relationship verified: Teaser column is before Price column in both source and dest")
        else:
            logger.warning("Position relationship mismatch between source and dest for special columns!")
    
    # Xử lý các cột khác bằng cách mapping thông thường
    for src_idx, src_header in enumerate(source_headers):
        # Bỏ qua cột A, B, C và các cột đặc biệt đã xử lý
        if src_idx in [0, 1, 2] or src_idx in price_col_indexes or src_idx in teaser_col_indexes:
            continue
        
        src_lower = src_header.lower()
        # Map trực tiếp nếu header hoàn toàn giống nhau
        for dest_idx, dest_header in enumerate(dest_headers):
            # Bỏ qua các cột đặc biệt đã xử lý ở đích
            if dest_idx in dest_price_col_indexes or dest_idx in dest_teaser_col_indexes:
                continue
                
            if src_lower == dest_header.lower():
                mapping[src_header] = dest_header
                logger.info(f"Exact match: '{src_header}' -> '{dest_header}'")
                break
    
    # Thêm các column matching dựa trên keyword
    key_columns = {
        'Mã shop (Shop ID)': ['mã shop', 'shop id'],
        'Mã sản phẩm': ['mã sản phẩm', 'item id'],
        'Tên sản phẩm': ['tên sản phẩm', 'product name'],
        'Giá gốc': ['giá gốc'],
        'Link sản phẩm': ['link', 'product link'],
        'Mã phân loại': ['mã phân loại', 'model id'],
        'Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?': ['mượn sản phẩm']
    }
    
    # Tìm các cột chưa được map
    unmapped_source = [h for h in source_headers if h not in mapping.keys() and source_headers.index(h) not in [0, 1, 2]]
    
    for src_header in unmapped_source:
        src_lower = src_header.lower()
        for dest_header in dest_headers:
            dest_lower = dest_header.lower()
            
            # Tìm keyword match
            for key_col, keywords in key_columns.items():
                if any(kw in src_lower for kw in keywords) and any(kw in dest_lower for kw in keywords):
                    mapping[src_header] = dest_header
                    logger.info(f"Keyword match: '{src_header}' -> '{dest_header}' using keywords {keywords}")
                    break
    
    # Kiểm tra và log các column chưa được map
    logger.info("=== MAPPING SUMMARY ===")
    mapped_sources = set(mapping.keys())
    for src in source_headers:
        if src not in mapped_sources and source_headers.index(src) not in [0, 1, 2]:
            logger.warning(f"Source header not mapped: '{src}'")
    
    # Log tất cả mapping để debug
    for src, dest in mapping.items():
        logger.info(f"Final mapping: '{src}' -> '{dest}'")
    
    return mapping

def get_headers_from_sheet(gs_service, spreadsheet_id: str, sheet_title: str) -> List[str]:
    result = gs_service.spreadsheets().get(
        spreadsheetId=spreadsheet_id,
        ranges=[f"'{sheet_title}'!A1:AH"],
        includeGridData=True
    ).execute()
    sheets_data = result.get('sheets', [])
    if not sheets_data:
        return []
    row_data = sheets_data[0].get("data", [])[0].get("rowData", [])
    header_row_idx = detect_header_row_custom(row_data)
    if header_row_idx == -1:
        return []
    header_row = row_data[header_row_idx].get("values", [])
    headers = [get_cell_info(cell)["value"].replace('\n', ' ').strip() if get_cell_info(cell)["value"] else f"Column_{i+1}"
               for i, cell in enumerate(header_row) if i < 34]
    return headers

def normalize_money_value(value_str: str) -> str:
    """
    Chuẩn hóa số tiền và xử lý cả hai kiểu định dạng (dấu chấm hoặc dấu phẩy phân cách hàng nghìn)
    bất kể locale của sheet nguồn.
    * Thêm dấu nháy đơn chỉ cho định dạng số có dấu chấm phân cách hàng nghìn (VN style)
    * Giữ nguyên định dạng số có dấu phẩy phân cách hàng nghìn (US style)
    """
    # Xử lý trường hợp value_str không phải là chuỗi
    if value_str is None:
        return ""
    if not isinstance(value_str, str):
        value_str = str(value_str)
    
    # Nếu chuỗi trống hoặc không chứa ký tự số, không xử lý
    if not value_str or not any(c.isdigit() for c in value_str):
        return value_str
        
    # Kiểm tra nếu giá trị đã có dấu nháy đơn ở đầu, giữ nguyên định dạng
    if value_str.startswith("'"):
        return value_str
    
    # Loại bỏ ký tự tiền tệ và khoảng trắng thừa
    original_value = value_str
    for symbol in ["₫", "đ", "VND", "vnd", " ", "\u200B"]:  # \u200B là zero-width space
        value_str = value_str.replace(symbol, "")
    value_str = value_str.strip()
    
    # PHƯƠNG PHÁP XỬ LÝ MỚI: Phát hiện ngữ cảnh chính xác của dấu chấm/phẩy
    # 1. Đếm số lượng dấu chấm và dấu phẩy
    dot_count = value_str.count(".")
    comma_count = value_str.count(",")
    
    # Nếu không có dấu chấm hoặc dấu phẩy, giữ nguyên
    if dot_count == 0 and comma_count == 0:
        return value_str
        
    # Kiểm tra các cấu trúc định dạng số khác nhau
    
    # Trường hợp UK/US: 1,234.56 (phẩy phân cách nghìn, chấm phân cách thập phân)
    # Hoặc VN/EU: 1.234,56 (chấm phân cách nghìn, phẩy phân cách thập phân)
    # Hoặc dạng đơn giản: 1.234 hoặc 1,234 (một dấu phân cách duy nhất)
    
    # Nếu chỉ có dấu chấm và dấu chấm cuối cùng ở vị trí thứ 3 từ cuối, nhiều khả năng là dấu thập phân
    # Ví dụ: 145.50, 12345.67, 0.25
    if dot_count == 1 and comma_count == 0:
        dot_pos = value_str.find('.')
        if len(value_str) - dot_pos <= 3:  # Có 1-2 chữ số sau dấu chấm
            # Đây có thể là số thập phân - GIỮ NGUYÊN
            return value_str
        else:
            # Đây có thể là dấu phân cách hàng nghìn kiểu VN: 145.000
            # Thêm dấu nháy đơn vào đầu để bảo vệ giá trị
            return f"'{value_str}"
            
    # Nếu chỉ có dấu phẩy và dấu phẩy cuối cùng ở vị trí thứ 3 từ cuối, nhiều khả năng là dấu thập phân
    if comma_count == 1 and dot_count == 0:
        comma_pos = value_str.find(',')
        if len(value_str) - comma_pos <= 3:  # Có 1-2 chữ số sau dấu phẩy
            # Đây có thể là số thập phân - biến đổi thành dạng dấu chấm
            return value_str.replace(',', '.')
        else:
            # Đây có thể là dấu phân cách hàng nghìn kiểu US: 145,000
            # THAY ĐỔI: Không thêm dấu nháy đơn cho định dạng dấu phẩy
            return value_str
    
    # Trường hợp nhiều dấu chấm hoặc nhiều dấu phẩy
    # Kiểm tra xem các dấu phân cách có cách đều nhau 3 ký tự không
    if dot_count > 0 and comma_count == 0:
        parts = value_str.split('.')
        # Nếu tất cả các phần sau phần đầu tiên đều có 3 chữ số, thì đây là dấu phân cách hàng nghìn
        # Ví dụ: 1.234.567
        if all(len(part) == 3 for part in parts[1:]):
            # Đây là dấu phân cách hàng nghìn kiểu VN: 1.234.567
            # Thêm dấu nháy đơn vào đầu để bảo vệ giá trị
            return f"'{value_str}"
    
    if comma_count > 0 and dot_count == 0:
        parts = value_str.split(',')
        # Nếu tất cả các phần sau phần đầu tiên đều có 3 chữ số, thì đây là dấu phân cách hàng nghìn
        # Ví dụ: 1,234,567
        if all(len(part) == 3 for part in parts[1:]):
            # Đây là dấu phân cách hàng nghìn kiểu US: 1,234,567
            # THAY ĐỔI: Không thêm dấu nháy đơn cho định dạng dấu phẩy
            return value_str
    
    # Trường hợp có cả dấu chấm và dấu phẩy
    if dot_count > 0 and comma_count > 0:
        # Xác định dấu cuối cùng để biết định dạng
        last_dot_pos = value_str.rfind('.')
        last_comma_pos = value_str.rfind(',')
        
        if last_dot_pos > last_comma_pos:
            # Định dạng US: 1,234.56 (dấu chấm là dấu thập phân)
            # Không thay đổi, đã đúng định dạng cho Google Sheets
            return value_str
        else:
            # Định dạng EU/VN: 1.234,56 (dấu phẩy là dấu thập phân)
            # Thêm dấu nháy đơn để giữ nguyên định dạng
            return f"'{value_str}"
    
    # Trong các trường hợp khác nếu có dấu chấm, đánh dấu với dấu nháy đơn
    # để đảm bảo giữ nguyên định dạng
    if dot_count > 0:
        return f"'{value_str}"
    
    # Trường hợp còn lại, giữ nguyên
    return value_str

def process_sheet_data(gs_service, spreadsheet_id: str, sheet: Dict, mapping: Dict[str, str], header_cache: Dict, start_row: int = None, preserve_number_format: bool = False) -> List[Dict[str, Any]]:
    header_result = get_header_mapping(gs_service, spreadsheet_id, sheet, header_cache)
    if not header_result:
        logger.warning(f"No header mapping for sheet {sheet['properties']['title']}")
        return []
    header_index, source_headers = header_result
    
    # Nếu start_row không được cung cấp, mặc định là dòng ngay sau header
    if start_row is None:
        start_row = header_index + 2
    else:
        # Đảm bảo start_row không nhỏ hơn dòng bắt đầu hợp lệ (dòng sau header)
        start_row = max(start_row, header_index + 2)
    
    logger.info(f"Starting data processing for sheet {sheet['properties']['title']} from row {start_row}")
    logger.info(f"preserve_number_format = {preserve_number_format}")

    # Tìm và đọc file danh sách dòng có dữ liệu
    import tempfile, os, json
    valid_rows = set()
    temp_dir = tempfile.gettempdir()
    rows_data_file = os.path.join(temp_dir, f"rows_with_data_{sheet['properties']['title']}.json")
    try:
        if os.path.exists(rows_data_file):
            with open(rows_data_file, "r") as f:
                data = json.load(f)
                if "rows" in data:
                    valid_rows = set([int(row) for row in data["rows"]])
                    logger.info(f"Loaded {len(valid_rows)} valid rows from file for sheet {sheet['properties']['title']}")
    except Exception as e:
        logger.error(f"Error loading valid rows file: {e}")
        # Nếu không đọc được file, tiếp tục xử lý tất cả các dòng như trước

    try:
        # CRITICAL CHANGE: Luôn sử dụng FORMATTED_VALUE và đảm bảo đọc dưới dạng chuỗi (string)
        value_render_option = 'FORMATTED_VALUE'  # Luôn dùng FORMATTED_VALUE để giữ định dạng số
        
        logger.info(f"Getting data with valueRenderOption='{value_render_option}'")
        
        result = gs_service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet['properties']['title']}'!A{start_row}:AH",
            valueRenderOption=value_render_option,
            dateTimeRenderOption='FORMATTED_STRING'
        ).execute()
        values = result.get('values', [])
        if not values:
            logger.warning(f"No data found in sheet {sheet['properties']['title']} starting from row {start_row}")
            return []

        records = []
        col_g_index = 6  # Cột G là cột thứ 7 (0-based index), dùng để kiểm tra dòng cuối

        # ======= PHƯƠNG PHÁP TỔNG HỢP: NHIỀU LỚP ĐỂ XÁC ĐỊNH CỘT ĐẶC BIỆT =======
        
        # STEP 1: First attempt - direct keyword search with MORE PRECISE conditions
        logger.info("STEP 1: Searching by precise keywords...")
        price_col_idx = -1
        teaser_col_idx = -1
        
        # Tìm các cột đặc biệt bằng từ khóa CỤ THỂ HƠN
        for idx, header in enumerate(source_headers):
            header_lower = header.lower()
            
            # Tìm cột "giá sau cùng" - tìm chính xác hơn
            if ('công bố giá' in header_lower or 'giá sau cùng' in header_lower) and ('cho phép' in header_lower or 'được quyền' in header_lower):
                price_col_idx = idx
                logger.info(f"[PRECISE-KEYWORD] Found price column at index {idx}: '{header}'")
            
            # Tìm cột "teaser" - tìm chính xác hơn với từ khóa cụ thể
            elif ('teaser video' in header_lower or 'teaser post' in header_lower) and ('cho phép' in header_lower or 'được sử dụng' in header_lower):
                teaser_col_idx = idx
                logger.info(f"[PRECISE-KEYWORD] Found teaser column at index {idx}: '{header}'")
            
            # Loại trừ rõ ràng "Link hình ảnh sản phẩm"
            elif 'link hình ảnh' in header_lower:
                logger.info(f"[EXCLUDE] Skipping 'Link hình ảnh sản phẩm' column at index {idx}: '{header}'")

        # STEP 2: If precise keyword search didn't work, fall back to more general search + context
        if price_col_idx == -1 or teaser_col_idx == -1:
            logger.info("STEP 2: Falling back to context-aware keyword search...")
            
            # Tạo danh sách chỉ mục của tất cả cột
            all_candidates = []
            for idx, header in enumerate(source_headers):
                header_lower = header.lower()
                
                # Điểm số cho mỗi loại cột dựa trên độ khớp với các từ khóa
                price_score = 0
                teaser_score = 0
                
                # Đánh giá điểm số cho cột giá sau cùng
                if 'công bố giá' in header_lower: price_score += 3
                if 'giá sau cùng' in header_lower: price_score += 3
                if 'được quyền' in header_lower and 'giá' in header_lower: price_score += 2
                if 'cho phép' in header_lower and 'giá' in header_lower: price_score += 2
                
                # Đánh giá điểm số cho cột teaser
                if 'teaser video' in header_lower: teaser_score += 3
                if 'teaser post' in header_lower: teaser_score += 3
                if 'được sử dụng' in header_lower and 'hình ảnh' in header_lower: teaser_score += 2
                if 'truyền thông' in header_lower: teaser_score += 1
                
                # GIẢM điểm cho các cột không liên quan
                if 'link hình' in header_lower: 
                    teaser_score = -5  # Loại trừ hoàn toàn "Link hình ảnh sản phẩm"
                
                # Lưu kết quả đánh giá
                all_candidates.append({
                    'index': idx,
                    'header': header,
                    'price_score': price_score,
                    'teaser_score': teaser_score
                })
            
            # Sắp xếp ứng viên theo điểm số
            price_candidates = sorted([c for c in all_candidates if c['price_score'] > 0], 
                                     key=lambda x: x['price_score'], reverse=True)
            teaser_candidates = sorted([c for c in all_candidates if c['teaser_score'] > 0], 
                                      key=lambda x: x['teaser_score'], reverse=True)
            
            # Log các ứng viên tiềm năng
            logger.info(f"Price candidates: {len(price_candidates)}")
            for c in price_candidates[:3]:  # Log top 3
                logger.info(f"  - Index {c['index']}, Score {c['price_score']}: '{c['header']}'")
                
            logger.info(f"Teaser candidates: {len(teaser_candidates)}")
            for c in teaser_candidates[:3]:  # Log top 3
                logger.info(f"  - Index {c['index']}, Score {c['teaser_score']}: '{c['header']}'")
            
            # Chọn ứng viên có điểm số cao nhất
            if price_col_idx == -1 and price_candidates:
                price_col_idx = price_candidates[0]['index']
                logger.info(f"[CONTEXT] Selected price column at index {price_col_idx}: '{price_candidates[0]['header']}'")
                
            if teaser_col_idx == -1 and teaser_candidates:
                teaser_col_idx = teaser_candidates[0]['index']
                logger.info(f"[CONTEXT] Selected teaser column at index {teaser_col_idx}: '{teaser_candidates[0]['header']}'")

        # STEP 3: If context search still failed, use fixed standard positions with verification
        if price_col_idx == -1 or teaser_col_idx == -1:
            logger.info("STEP 3: Using fixed standard positions with verification...")
            
            # Cột AD (29) và AE (30) là vị trí chuẩn
            if price_col_idx == -1 and len(source_headers) > 29:
                candidate_header = source_headers[29].lower()
                # Xác minh nội dung trước khi chấp nhận
                if ('công bố giá' in candidate_header or 'giá sau cùng' in candidate_header) and 'link hình' not in candidate_header:
                    price_col_idx = 29
                    logger.info(f"[VERIFIED-FIXED] Using standard price column at index 29: '{source_headers[29]}'")
            
            if teaser_col_idx == -1 and len(source_headers) > 30:
                candidate_header = source_headers[30].lower()
                # Xác minh nội dung trước khi chấp nhận
                if ('teaser' in candidate_header or 'hình ảnh' in candidate_header) and 'link hình' not in candidate_header:
                    teaser_col_idx = 30
                    logger.info(f"[VERIFIED-FIXED] Using standard teaser column at index 30: '{source_headers[30]}'")

        # STEP 4: Use relationship between columns if we found one but not the other
        if price_col_idx != -1 and teaser_col_idx == -1:
            # Nếu đã tìm thấy price nhưng chưa tìm thấy teaser, giả định teaser ở cột kế bên
            if price_col_idx + 1 < len(source_headers):
                candidate_header = source_headers[price_col_idx + 1].lower()
                # Xác minh nội dung trước khi chấp nhận
                if 'link hình' not in candidate_header:
                    teaser_col_idx = price_col_idx + 1
                    logger.info(f"[RELATIONSHIP] Assuming teaser column next to price: index {teaser_col_idx}, header '{source_headers[teaser_col_idx]}'")
        elif teaser_col_idx != -1 and price_col_idx == -1:
            # Nếu đã tìm thấy teaser nhưng chưa tìm thấy price, giả định price ở cột kế bên
            if teaser_col_idx - 1 >= 0:
                candidate_header = source_headers[teaser_col_idx - 1].lower()
                # Xác minh nội dung trước khi chấp nhận
                if 'link hình' not in candidate_header:
                    price_col_idx = teaser_col_idx - 1
                    logger.info(f"[RELATIONSHIP] Assuming price column before teaser: index {price_col_idx}, header '{source_headers[price_col_idx]}'")

        # STEP 5: If all else fails, use anchor columns with distance check
        if price_col_idx == -1 or teaser_col_idx == -1:
            logger.info("STEP 5: Using anchor columns to calculate offset...")
            
            # Tìm cột "Giá gốc" làm điểm neo
            anchor_idx = -1
            EXPECTED_ANCHOR_INDEX = 11  # Thường cột "Giá gốc" ở vị trí L (index 11)
            
            for idx, header in enumerate(source_headers):
                header_lower = header.lower()
                if 'giá gốc' in header_lower:
                    anchor_idx = idx
                    logger.info(f"Found anchor 'Giá gốc' at index {idx}: '{header}'")
                    break
            
            if anchor_idx != -1:
                # Tính offset (độ lệch) từ vị trí thực tế so với vị trí chuẩn
                offset = anchor_idx - EXPECTED_ANCHOR_INDEX
                logger.info(f"Calculated offset: {offset} (anchor at {anchor_idx}, standard at {EXPECTED_ANCHOR_INDEX})")
                
                # Điều chỉnh vị trí cột price và teaser theo offset
                # Khoảng cách từ Giá gốc đến các cột đặc biệt thường là cố định
                STANDARD_DISTANCE_TO_PRICE = 29 - 11  # AD(29) - L(11) = 18
                STANDARD_DISTANCE_TO_TEASER = 30 - 11  # AE(30) - L(11) = 19
                
                adjusted_price_idx = anchor_idx + STANDARD_DISTANCE_TO_PRICE
                adjusted_teaser_idx = anchor_idx + STANDARD_DISTANCE_TO_TEASER
                
                logger.info(f"Adjusted indices based on anchor: price={adjusted_price_idx}, teaser={adjusted_teaser_idx}")
                
                # Kiểm tra và áp dụng các chỉ số đã điều chỉnh nếu chúng hợp lệ
                if price_col_idx == -1 and 0 <= adjusted_price_idx < len(source_headers):
                    price_col_idx = adjusted_price_idx
                    logger.info(f"[ANCHOR-ADJUSTED] Using price at index {price_col_idx}: '{source_headers[price_col_idx]}'")
                
                if teaser_col_idx == -1 and 0 <= adjusted_teaser_idx < len(source_headers):
                    teaser_col_idx = adjusted_teaser_idx
                    logger.info(f"[ANCHOR-ADJUSTED] Using teaser at index {teaser_col_idx}: '{source_headers[teaser_col_idx]}'")

        # STEP 6: Find destination headers for our special columns
        price_dest_header = None
        teaser_dest_header = None
        
        # Tìm destination headers từ mapping
        if price_col_idx >= 0 and price_col_idx < len(source_headers):
            price_source_header = source_headers[price_col_idx]
            price_dest_header = mapping.get(price_source_header)
            if price_dest_header:
                logger.info(f"Found price destination through mapping: '{price_dest_header}'")
        
        if teaser_col_idx >= 0 and teaser_col_idx < len(source_headers):
            teaser_source_header = source_headers[teaser_col_idx]
            teaser_dest_header = mapping.get(teaser_source_header)
            if teaser_dest_header:
                logger.info(f"Found teaser destination through mapping: '{teaser_dest_header}'")
        
        # STEP 7: If mapping didn't provide dest headers, use standard positions or keywords
        if not price_dest_header or not teaser_dest_header:
            logger.info("STEP 7: Finding destination headers by standard positions and keywords...")
            
            # Tìm theo từ khóa trong dest headers
            dest_headers_values = list(mapping.values())
            
            for idx, header in enumerate(dest_headers_values):
                header_lower = header.lower() if header else ""
                if not price_dest_header and ('công bố giá' in header_lower or 'giá sau cùng' in header_lower):
                    price_dest_header = header
                    logger.info(f"Found price destination by keyword: '{price_dest_header}'")
                elif not teaser_dest_header and ('hình ảnh' in header_lower or 'teaser' in header_lower):
                    teaser_dest_header = header
                    logger.info(f"Found teaser destination by keyword: '{teaser_dest_header}'")

            # Nếu không tìm được bằng từ khóa, dùng vị trí chuẩn của sheet đích
            DEST_STANDARD_PRICE_INDEX = 30  # AE
            DEST_STANDARD_TEASER_INDEX = 31  # AF
            
            if not price_dest_header and len(dest_headers_values) > DEST_STANDARD_PRICE_INDEX:
                price_dest_header = dest_headers_values[DEST_STANDARD_PRICE_INDEX]
                logger.info(f"Using standard destination price at index {DEST_STANDARD_PRICE_INDEX}: '{price_dest_header}'")
            
            if not teaser_dest_header and len(dest_headers_values) > DEST_STANDARD_TEASER_INDEX:
                teaser_dest_header = dest_headers_values[DEST_STANDARD_TEASER_INDEX]
                logger.info(f"Using standard destination teaser at index {DEST_STANDARD_TEASER_INDEX}: '{teaser_dest_header}'")
            
            # Nếu tìm được một cột nhưng không tìm được cột còn lại, áp dụng logic quan hệ
            if price_dest_header and not teaser_dest_header:
                # Tìm index của price_dest_header
                for idx, header in enumerate(dest_headers_values):
                    if header == price_dest_header and idx + 1 < len(dest_headers_values):
                        teaser_dest_header = dest_headers_values[idx + 1]
                        logger.info(f"Using adjacent column as teaser destination: '{teaser_dest_header}'")
                        break
            
            elif teaser_dest_header and not price_dest_header:
                # Tìm index của teaser_dest_header
                for idx, header in enumerate(dest_headers_values):
                    if header == teaser_dest_header and idx - 1 >= 0:
                        price_dest_header = dest_headers_values[idx - 1]
                        logger.info(f"Using adjacent column as price destination: '{price_dest_header}'")
                        break
        
        # Log kết quả cuối cùng
        logger.info("=== FINAL COLUMN MAPPING ===")
        logger.info(f"SOURCE: price_idx={price_col_idx}, teaser_idx={teaser_col_idx}")
        if price_col_idx >= 0 and price_col_idx < len(source_headers):
            logger.info(f"Price header: '{source_headers[price_col_idx]}'")
        if teaser_col_idx >= 0 and teaser_col_idx < len(source_headers):
            logger.info(f"Teaser header: '{source_headers[teaser_col_idx]}'")
        logger.info(f"DESTINATION: price='{price_dest_header}', teaser='{teaser_dest_header}'")
        
        # Xác định các cột cần áp dụng chuyển đổi yes/no - SỬA ĐỔI ĐỂ ÁP DỤNG CHUẨN HÓA CHÍNH XÁC
        yes_no_column_idxs = []
        
        # Hàm chuẩn hóa text cho việc so sánh header Yes/No
        def normalize_text_for_comparison(text):
            if not text:
                return ""
            
            import unicodedata
            import re
            # Chuyển về chữ thường
            text = text.lower()
            # Loại bỏ dấu
            text = unicodedata.normalize('NFKD', text)
            text = ''.join([c for c in text if not unicodedata.combining(c)])
            # Loại bỏ các ký tự đặc biệt, giữ lại chữ cái, số
            text = re.sub(r'[^\w\s]', '', text)
            # Loại bỏ khoảng trắng hoàn toàn
            text = re.sub(r'\s+', '', text)
            return text
        
        # Danh sách các cột Yes/No cụ thể cần áp dụng chuyển đổi
        target_yes_no_headers = [
            "Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?",
            "KOL có được sử dụng sản phẩm Nhà bán hàng cho mượn trong quá trình diễn ra Livestream với mục đích review sản phẩm này không?",
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?*Giá sau cùng: là giá sản phẩm sau khi đã áp dụng: mã giảm giá của Nhà Bán Hàng (nếu có), trợ giá của Shopee (nếu có) và tất cả mã giảm giá của từ Shopee.",
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được sử dụng hình ảnh/thông tin sản phẩm vào trong teaser video/teaser post cho mục đích truyền thông về phiên Livestream ?"
        ]
        
        # Chuẩn hóa tất cả các header mục tiêu
        normalized_target_headers = {normalize_text_for_comparison(h): h for h in target_yes_no_headers}
        logger.info(f"Chuẩn hóa header cho so sánh Yes/No:")
        for norm, orig in normalized_target_headers.items():
            logger.info(f"  - '{orig}' -> '{norm}'")
        
        # Kiểm tra từng header nguồn
        for idx, src_header in enumerate(source_headers):
            if not src_header:
                continue
                
            # Chuẩn hóa header nguồn
            src_normalized = normalize_text_for_comparison(src_header)
            
            # Kiểm tra độ tương đồng với từng header mục tiêu
            for norm_target, orig_target in normalized_target_headers.items():
                # Tính toán độ tương đồng
                similarity = 0
                
                if src_normalized and norm_target:
                    # So sánh trực tiếp chuỗi đã chuẩn hóa
                    if src_normalized == norm_target:
                        similarity = 1.0  # 100% giống nhau
                    else:
                        # Tính tỷ lệ tương đồng dựa trên số ký tự giống nhau
                        common_chars = set(src_normalized) & set(norm_target)
                        if common_chars:
                            similarity = len(common_chars) / max(len(src_normalized), len(norm_target))
                
                # Chỉ chấp nhận header có độ tương đồng >= 99%
                if similarity >= 0.99:
                    yes_no_column_idxs.append(idx)
                    logger.info(f"Will apply yes/no conversion to column {idx}: '{src_header}'")
                    logger.info(f"  - Matched with: '{orig_target}'")
                    logger.info(f"  - Similarity score: {similarity:.2f}")
                    logger.info(f"  - Normalized source: '{src_normalized}'")
                    logger.info(f"  - Normalized target: '{norm_target}'")
                    break
            
        # Nếu không tìm thấy cột nào phù hợp, ghi log
        if not yes_no_column_idxs:
            logger.warning("No Yes/No columns found for conversion using the strict 99% similarity matching")
        else:
            logger.info(f"Found {len(yes_no_column_idxs)} Yes/No columns for conversion: {', '.join([source_headers[idx] for idx in yes_no_column_idxs])}")
            
        # Loại bỏ code xác định cột Yes/No dựa trên anchor cũ
        # (Giữ lại các đoạn xử lý các cột tiền tệ và cột số khác)
        
        # Thêm danh sách cột tiền tệ vào trước đoạn xử lý dữ liệu
        # Danh sách các cột cần giữ nguyên định dạng
        preserve_format_columns = [
            "Giá gốc",
            "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
            "Mức giảm tối đa",
            "Áp dụng cho đơn từ"
        ]
        
        # Danh sách tất cả các cột tiền tệ cần xử lý đặc biệt
        money_columns = [
            "Giá gốc",
            "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
            "Giá bán dự kiến",
            "Giá bán còn lại sau khi trừ chi phí",
            "Giá sau cùng sau khi trừ tất cả discount",
            "Giá trị quà tặng kèm",
            "Mức giảm tối đa",
            "Áp dụng cho đơn từ",
            "Số lượng tồn kho",
            "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
        ]
        
        # Xác định danh sách các cột số cần giữ nguyên định dạng nếu preserve_number_format=True
        additional_preserve_columns = []
        if preserve_number_format:
            additional_preserve_columns = [
                "Giá gốc",
                "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
                "Mức giảm tối đa",
                "Áp dụng cho đơn từ",
                "Giá bán dự kiến",
                "Giá bán còn lại sau khi trừ chi phí",
                "Giá sau cùng sau khi trừ tất cả discount",
                "Giá trị quà tặng kèm",
                "Số lượng tồn kho",
                "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
            ]
            logger.info(f"Will preserve original format for additional columns when preserve_number_format=True: {additional_preserve_columns}")
        else:
            logger.info(f"Will preserve original format for columns: {preserve_format_columns}")
        
        # Xử lý từng dòng dữ liệu, chỉ lấy những dòng hợp lệ
        for row_idx, row in enumerate(values):
            # Kiểm tra xem dòng này có nằm trong danh sách dòng hợp lệ không
            current_row = row_idx + start_row
            
            # Bỏ qua dòng nếu không nằm trong danh sách dòng hợp lệ và danh sách không rỗng
            if valid_rows and current_row not in valid_rows:
                logger.debug(f"Skipping row {current_row} as it's not in the valid rows list")
                continue
            
            record = {'_source_row': current_row, '_source_sheet': sheet['properties']['title']}
            
            # Áp dụng chuyển đổi giá trị yes/no
            for col_idx in yes_no_column_idxs:
                if col_idx < len(row) and row[col_idx] is not None:
                    # Chuyển đổi giá trị yes/no thành chuẩn ĐỒNG Ý/KHÔNG ĐỒNG Ý
                    orig_value = str(row[col_idx]).strip()
                    new_value = convert_yes_no_value(orig_value)
                    
                    if new_value != orig_value:
                        logger.info(f"Converted '{orig_value}' to '{new_value}' in column {source_headers[col_idx]}")
                        row[col_idx] = new_value
            
            # Xử lý tất cả các cột được ánh xạ
            for src_header, dest_header in mapping.items():
                try:
                    src_idx = source_headers.index(src_header)
                    if src_idx < 3:  # Bỏ qua cột A, B, C
                        continue
                    
                    if src_idx < len(row):
                        value = row[src_idx]
                        
                        # Xử lý đặc biệt cho các cột tiền tệ
                        if src_header in money_columns and value is not None:
                            # Chuẩn hóa định dạng số tiền để xử lý cả dấu chấm và dấu phẩy
                            original_value = value
                            value = normalize_money_value(str(value))
                            if value != original_value:
                                logger.debug(f"Normalized money value from '{original_value}' to '{value}'")
                        
                        # Nếu preserve_number_format=True hoặc cột trong danh sách giữ nguyên, không xử lý giá trị
                        if preserve_number_format and src_header in additional_preserve_columns:
                            # Giữ nguyên giá trị
                            logger.debug(f"Preserving original format for '{src_header}': {value}")
                            pass
                        elif src_header in preserve_format_columns:
                            # Giữ nguyên giá trị
                            logger.debug(f"Preserving original format for '{src_header}': {value}")
                            pass
                        
                        # Giữ nguyên giá trị cho các cột cần bảo toàn định dạng hoặc khi preserve_number_format=True
                        record[dest_header] = value if value is not None else ""
                except (ValueError, IndexError):
                    # Có thể header không tồn tại trong source_headers hoặc index out of range
                    pass
            
            # Xử lý đặc biệt cho 2 cột quan trọng - ưu tiên cao nhất
            # 1. Cột "giá sau cùng"
            if price_col_idx >= 0 and price_dest_header and price_col_idx < len(row):
                value = row[price_col_idx]
                record[price_dest_header] = value if value is not None else ""
                logger.debug(f"Direct copy price: row {current_row}, value={value} -> '{price_dest_header}'")
            
            # 2. Cột "teaser"
            if teaser_col_idx >= 0 and teaser_dest_header and teaser_col_idx < len(row):
                value = row[teaser_col_idx]
                record[teaser_dest_header] = value if value is not None else ""
                logger.debug(f"Direct copy teaser: row {current_row}, value={value} -> '{teaser_dest_header}'")
            
            # Ép buộc sao chép cột G (Shop ID) từ sheet nguồn
            if col_g_index < len(row):
                record['Mã shop (Shop ID)'] = row[col_g_index] if row[col_g_index] is not None else ""
            
            # Sửa thành: chỉ dừng nếu giá trị ở cột G là rỗng - không phải break sau khi xử lý dòng đầu tiên
            if len(row) <= col_g_index or not row[col_g_index]:
                logger.info(f"Empty Shop ID found at row {current_row}, checking if we should include this row")
                # Chỉ thêm nếu có dữ liệu trong các cột khác
                if any(record.get(key, "") for key in record.keys() if key not in ['_source_row', '_source_sheet']):
                    records.append(record)
                    logger.info(f"Row {current_row} has data in other columns, including in results")
                else:
                    logger.info(f"Row {current_row} is empty, this might be the end of data")
                continue  # Thay break bằng continue để kiểm tra hàng tiếp theo
            
            # Thêm record vào danh sách kết quả nếu có dữ liệu (không rỗng)
            if any(record.get(key, "") for key in record.keys() if key not in ['_source_row', '_source_sheet']):
                records.append(record)
        
        logger.info(f"Sheet '{sheet['properties']['title']}' processed, total rows: {len(records)}")
        return records
    except Exception as e:
        logger.error(f"Error processing sheet {sheet['properties']['title']}: {e}")
        return []

def convert_yes_no_value(value: str) -> str:
    """
    Improved conversion of yes/no variants to standardized ĐỒNG Ý/KHÔNG ĐỒNG Ý format
    Ensures consistent UTF-8 representation
    """
    if not value or not isinstance(value, str):
        return value
        
    value = str(value).strip()
    value_lower = value.lower()
    
    # Define standard UTF-8 versions of our target strings
    # These ensure consistent byte representation regardless of source
    DONG_Y = "ĐỒNG Ý".encode('utf-8').decode('utf-8')
    KHONG_DONG_Y = "KHÔNG ĐỒNG Ý".encode('utf-8').decode('utf-8')
    
    # Expanded list of yes terms
    yes_terms = ["yes", "y", "có", "đồng ý", "dong y", "đồng ý.", "co", "ok"]
    
    # Expanded list of no terms
    no_terms = ["no", "n", "không", "không đồng ý", "khong", "khong dong y"]
    
    # Check exact matches first
    if value_lower in yes_terms:
        return DONG_Y
        
    if value_lower in no_terms:
        return KHONG_DONG_Y
    
    # Check partial matches
    for term in yes_terms:
        if term in value_lower:
            return DONG_Y
            
    for term in no_terms:
        if term in value_lower:
            return KHONG_DONG_Y
    
    # No match found, return original - không tự động chuyển đổi giá trị không xác định
    return value

def check_existing_data_in_column_e(gs_service, spreadsheet_id: str, sheet_title: str) -> Tuple[bool, int]:
    try:
        result = gs_service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_title}'!E4:E",
            valueRenderOption='UNFORMATTED_VALUE'
        ).execute()
        values = result.get('values', [])
        has_data = False
        last_row_with_data = 3
        for row_idx, row in enumerate(values):
            if row and row[0]:
                has_data = True
                last_row_with_data = row_idx + 4
            logger.debug(f"Row {row_idx + 4} in column E: {row[0] if row and row[0] else 'Empty'}")
        logger.info(f"Checked existing data in {sheet_title}, has_data: {has_data}, last_row_with_data: {last_row_with_data}")
        return has_data, last_row_with_data
    except Exception as e:
        logger.error(f"Error checking existing data in column E: {e}")
        return False, 3

# Helper function to get validation values directly from the sheet
def get_validation_values(gs_service, spreadsheet_id: str, sheet_title: str, yes_no_columns: List[str], headers: List[str]) -> Dict[str, List[str]]:
    """
    Extract the exact validation values from Google Sheets for specific columns
    to ensure we use the exact format expected by data validation
    """
    validation_values = {}
    try:
        # Get the sheet data including data validation rules
        result = gs_service.spreadsheets().get(
            spreadsheetId=spreadsheet_id,
            ranges=[f"'{sheet_title}'!A1:AH10"],
            includeGridData=True
        ).execute()
        
        sheets_data = result.get('sheets', [])
        if not sheets_data:
            return validation_values
        
        # Find row with headers
        grid_data = sheets_data[0].get('data', [{}])[0]
        row_data = grid_data.get('rowData', [])
        header_row_idx = detect_header_row_custom(row_data)
        
        if header_row_idx == -1 or header_row_idx + 1 >= len(row_data):
            return validation_values
        
        # Check cells in rows after header for data validation
        for col_name in yes_no_columns:
            try:
                col_idx = headers.index(col_name)
                # Look at rows 4-10 for validation rules
                for row_idx in range(header_row_idx + 1, min(header_row_idx + 8, len(row_data))):
                    if 'values' not in row_data[row_idx]:
                        continue
                        
                    cell_values = row_data[row_idx].get('values', [])
                    if col_idx >= len(cell_values):
                        continue
                    
                    cell = cell_values[col_idx]
                    if 'dataValidation' in cell:
                        data_validation = cell['dataValidation']
                        if 'condition' in data_validation and data_validation['condition'].get('type') == 'ONE_OF_LIST':
                            values_list = [val.get('userEnteredValue') for val in data_validation['condition'].get('values', [])]
                            if values_list:
                                validation_values[col_name] = values_list
                                logger.info(f"Found validation values for {col_name}: {values_list}")
                                break
            except (ValueError, IndexError) as e:
                continue
        
        return validation_values
                
    except Exception as e:
        logger.error(f"Error getting validation values: {e}")
        return validation_values

def setup_data_validation_for_yes_no_columns(gs_service, spreadsheet_id, sheet_id, headers):
    """
    Thiết lập data validation và conditional formatting cho các cột có giá trị Yes/No
    Args:
        gs_service: Google Sheets service object
        spreadsheet_id: ID của spreadsheet
        sheet_id: ID của sheet
        headers: Danh sách các tiêu đề cột
    """
    try:
        # Hàm loại bỏ dấu và các ký tự đặc biệt từ chuỗi tiếng Việt
        def normalize_text(text):
            if not text:
                return ""
            
            import unicodedata
            import re
            # Chuyển về chữ thường
            text = text.lower()
            # Loại bỏ dấu
            text = unicodedata.normalize('NFKD', text)
            text = ''.join([c for c in text if not unicodedata.combining(c)])
            # Loại bỏ các ký tự đặc biệt, giữ lại chữ cái, số và khoảng trắng
            text = re.sub(r'[^\w\s]', ' ', text)
            # Chuẩn hóa khoảng trắng và xóa hoàn toàn
            text = re.sub(r'\s+', '', text).strip()
            return text

        # Danh sách các cột Yes/No cần áp dụng data validation - Cập nhật với 4 tiêu đề chính xác theo yêu cầu
        target_headers = [
            "Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?",
            "KOL có được sử dụng sản phẩm Nhà bán hàng cho mượn trong quá trình diễn ra Livestream với mục đích review sản phẩm này không?",
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được quyền công bố giá sau cùng*trên các nền tảng mạng xã hội cho mục đích truyền thông về phiên Livestream?*Giá sau cùng: là giá sản phẩm sau khi đã áp dụng: mã giảm giá của Nhà Bán Hàng (nếu có), trợ giá của Shopee (nếu có) và tất cả mã giảm giá của từ Shopee.",
            "Cho phép Shopee, Beyond-K, Diệp Lê và các KOL tham gia phiên Livestream được sử dụng hình ảnh/thông tin sản phẩm vào trong teaser video/teaser post cho mục đích truyền thông về phiên Livestream ?"
        ]
        
        # Chuẩn hóa danh sách target_headers để so khớp chính xác hơn
        normalized_target_headers = {}
        for header in target_headers:
            normalized = normalize_text(header)
            normalized_target_headers[normalized] = header
            # Log để debug
            logger.info(f"Normalized header: '{header}' -> '{normalized}'")
        
        # Xóa các từ khóa đặc trưng và thay bằng cơ chế tương đồng chính xác
        
        # Danh sách các cột Yes/No cũ dùng để hỗ trợ tương thích ngược
        legacy_yes_no_columns = []  # Xóa danh sách cũ để tránh xử lý nhầm
        
        # Tìm các cột Yes/No trong headers với độ tương đồng 99%
        target_columns = []
        
        # Log tất cả headers để debug
        logger.info("Analyzing all headers in sheet:")
        for idx, header in enumerate(headers):
            if header:
                logger.info(f"Header[{idx}]: '{header}'")
                
        # Chỉ khớp chính xác với các header đã chuẩn hóa
        for idx, header in enumerate(headers):
            if not header:
                continue
                
            # Chuẩn hóa header hiện tại để so sánh
            normalized_header = normalize_text(header)
            
            # Tìm mức độ tương đồng cao (99%) với các header mục tiêu
            for norm_target, original_target in normalized_target_headers.items():
                # Tính toán độ tương đồng bằng phương pháp chuỗi con chung dài nhất (LCS)
                similarity = 0
                
                if normalized_header and norm_target:
                    # So sánh trực tiếp chuỗi sau khi đã chuẩn hóa
                    if normalized_header == norm_target:
                        similarity = 1.0  # 100% giống nhau
                    else:
                        # Tính tỷ lệ tương đồng dựa trên số ký tự giống nhau
                        common_chars = set(normalized_header) & set(norm_target)
                        if common_chars:
                            similarity = len(common_chars) / max(len(normalized_header), len(norm_target))
                
                # Chỉ chấp nhận độ tương đồng 99%
                if similarity >= 0.99:
                    target_columns.append(idx)
                    logger.info(f"✅ Exact match (99%+) for column '{header}' at index {idx} with '{original_target}'")
                    logger.info(f"Similarity score: {similarity:.2f}")
                    break
        
        # Remove duplicates but keep order
        target_columns = list(dict.fromkeys(target_columns))
        
        # Define the dropdown values
        yes_no_values = ["ĐỒNG Ý", "KHÔNG ĐỒNG Ý"]
        
        # Define the exact requested colors in decimal RGB (0-1 scale)
        yes_color = {
            "red": 212/255,    # 212, 237, 188 - light green background
            "green": 237/255,
            "blue": 188/255
        }
        
        no_color = {
            "red": 255/255,    # 255, 207, 201 - light red background
            "green": 207/255,
            "blue": 201/255
        }

        # Define text colors in decimal RGB (0-1 scale)
        yes_text_color = {
            "red": 17/255,     # 17, 115, 75 - dark green text
            "green": 115/255,
            "blue": 75/255
        }

        no_text_color = {
            "red": 177/255,    # 177, 2, 2 - dark red text
            "green": 2/255,
            "blue": 2/255
        }
        
        # Create validation rules and conditional formatting
        requests = []
        
        # First, clear any existing conditional formatting
        clear_conditional_format_rules(gs_service, spreadsheet_id, sheet_id)
        
        logger.info(f"Found {len(target_columns)} Yes/No columns to set up data validation")
        
        # For each target column
        for col_idx in target_columns:
            # Find column letter for logging
            col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx // 26) + chr(65 + col_idx % 26)
            
            # Try to find the column header
            if col_idx < len(headers):
                col_name = headers[col_idx]
                logger.info(f"Setting up data validation for column {col_letter} ({col_name})")
            else:
                logger.warning(f"Column index {col_idx} ({col_letter}) is out of range")
                continue
            
            # 1. Add data validation rule (dropdown) - THIS IS THE DATA VALIDATION PART
            requests.append({
                "setDataValidation": {
                    "range": {
                        "sheetId": sheet_id,
                        "startRowIndex": 3,  # Row 4 (0-based)
                        "startColumnIndex": col_idx,
                        "endColumnIndex": col_idx + 1
                    },
                    "rule": {
                        "condition": {
                            "type": "ONE_OF_LIST",
                            "values": [{"userEnteredValue": value} for value in yes_no_values]
                        },
                        "showCustomUi": True,
                        "strict": False
                    }
                }
            })
            
            # 2. Add conditional formatting for "ĐỒNG Ý" values (with background and text color)
            requests.append({
                "addConditionalFormatRule": {
                    "rule": {
                        "ranges": [{
                            "sheetId": sheet_id,
                            "startRowIndex": 3,  # Row 4 (0-based)
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                        }],
                        "booleanRule": {
                            "condition": {
                                "type": "TEXT_EQ",
                                "values": [{"userEnteredValue": "ĐỒNG Ý"}]
                            },
                            "format": {
                                "backgroundColor": yes_color,
                                "textFormat": {
                                    "foregroundColor": yes_text_color
                                }
                            }
                        }
                    },
                    "index": 0
                }
            })
            
            # 3. Add conditional formatting for "KHÔNG ĐỒNG Ý" values (with background and text color)
            requests.append({
                "addConditionalFormatRule": {
                    "rule": {
                        "ranges": [{
                            "sheetId": sheet_id,
                            "startRowIndex": 3,  # Row 4 (0-based)
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                        }],
                        "booleanRule": {
                            "condition": {
                                "type": "TEXT_EQ",
                                "values": [{"userEnteredValue": "KHÔNG ĐỒNG Ý"}]
                            },
                            "format": {
                                "backgroundColor": no_color,
                                "textFormat": {
                                    "foregroundColor": no_text_color
                                }
                            }
                        }
                    },
                    "index": 1
                }
            })
        
            # 4. Thêm conditional formatting cho các từ tương đương "Yes"
            yes_equivalents = ["Yes", "yes", "Y", "y", "có", "Có", "OK", "ok", "Ok", "đồng ý", "Đồng ý"]
            for i, value in enumerate(yes_equivalents):
                requests.append({
                    "addConditionalFormatRule": {
                        "rule": {
                            "ranges": [{
                            "sheetId": sheet_id,
                                "startRowIndex": 3,
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                            }],
                            "booleanRule": {
                                "condition": {
                                    "type": "TEXT_CONTAINS",
                                    "values": [{"userEnteredValue": value}]
                        },
                                "format": {
                                    "backgroundColor": yes_color,
                                "textFormat": {
                                        "foregroundColor": yes_text_color
                                    }
                                }
                            }
                        },
                        "index": 2 + i
                    }
                })
            
            # 5. Thêm conditional formatting cho các từ tương đương "No"
            no_equivalents = ["No", "no", "N", "n", "không", "Không", "khong", "Khong"]
            for i, value in enumerate(no_equivalents):
                requests.append({
                    "addConditionalFormatRule": {
                        "rule": {
                            "ranges": [{
                            "sheetId": sheet_id,
                                "startRowIndex": 3,
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                            }],
                            "booleanRule": {
                                "condition": {
                                    "type": "TEXT_CONTAINS",
                                    "values": [{"userEnteredValue": value}]
                                },
                                "format": {
                                    "backgroundColor": no_color,
                                    "textFormat": {
                                        "foregroundColor": no_text_color
                                    }
                                }
                            }
                        },
                        "index": 2 + len(yes_equivalents) + i
                    }
                })
        
        # Apply all requests in one batch
        if requests:
            body = {"requests": requests}
            gs_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()
            logger.info(f"Successfully set up data validation and formatting for {len(target_columns)} Yes/No columns")
        
        return True
    except Exception as e:
        logger.error(f"Error setting up column formatting: {str(e)}")
        return False

def save_temp_shop_ids(shop_ids: List[str]) -> None:
    unique_ids = list(set(shop_ids))
    temp_dir = tempfile.gettempdir()
    temp_file = os.path.join(temp_dir, "raw_processed_shop_ids.txt")
    with open(temp_file, "w", encoding="utf-8") as f:
        for sid in unique_ids:
            f.write(f"{sid}\n")

def save_temp_product_codes(product_codes: List[str]) -> None:
    unique_codes = list(set(product_codes))
    temp_dir = tempfile.gettempdir()
    temp_file = os.path.join(temp_dir, "raw_processed_product_ids.txt")
    with open(temp_file, "w", encoding="utf-8") as f:
        for code in unique_codes:
            f.write(code.strip() + "\n")

def save_temp_dest_data(dest_id: str, sheet_name: str) -> None:
    temp_dir = tempfile.gettempdir()
    temp_file = os.path.join(temp_dir, "raw_processed_dest.txt")
    with open(temp_file, "w", encoding="utf-8") as f:
        f.write(dest_id.strip() + "\n")
        f.write(sheet_name.strip() + "\n")

def generate_preview(gs_service, spreadsheet_id: str, sheet_title: str) -> Tuple[List[str], List[List[str]]]:
    try:
        result = gs_service.spreadsheets().values().get(
            spreadsheetId=spreadsheet_id,
            range=f"'{sheet_title}'!A4:AH"
        ).execute()
        values = result.get('values', [])
        if not values:
            return [], []
        data = values
        return [], data
    except Exception as e:
        logger.error(f"Error generating preview: {e}")
        return [], []

def commit_final_data(gs_service, spreadsheet_id: str, sheet_title: str, local_cache: List[List[Dict[str, Any]]]) -> bool:
    logger.info("Final commit completed. Data on destination sheet is considered official.")
    return True

def process_copied_data(data: List[Dict[str, Any]], dest_headers: List[str], preserve_number_format: bool = False) -> Tuple[List[Dict[str, Any]], Dict[str, List[Dict[str, Any]]]]:
    """
    Xử lý dữ liệu sau khi đã copy từ nguồn sang đích - 
    Bước này chỉ xử lý các vấn đề như định dạng số, ID, v.v. không xử lý yes/no values nữa
    
    Args:
        data: Dữ liệu cần xử lý
        dest_headers: Danh sách các header đích
        preserve_number_format: Nếu True, giữ nguyên định dạng số từ sheet nguồn
    
    Returns:
        Tuple chứa dữ liệu đã xử lý và danh sách các vấn đề tìm thấy
    """
    logger.info(f"Processing {len(data)} records with data cleanup rules")

    # Danh sách các cột cần xóa dữ liệu nếu giá trị là "x", "X", "không", v.v.
    columns_to_clean = [
        "Tên phân loại", 
        "Quà tặng kèm (nếu có)", 
        "Mã quà tặng kèm (Item ID quà tặng) nếu có", 
        "Giá trị quà tặng kèm", 
        "Link quà tặng kèm", 
        "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)",
        "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
        "Mức giảm tối đa", 
        "Áp dụng cho đơn từ" 
    ]
    # Danh sách các giá trị cần bị xóa
    values_to_remove = ["x", "X", "không", "Không", "Không có", "No gift"]
    
    # Danh sách các cột cần giữ nguyên định dạng theo yêu cầu
    preserve_format_columns = [
        "Giá gốc",
        "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
        "Mức giảm tối đa",
        "Áp dụng cho đơn từ"
    ]
    
    # Danh sách các cột cần định dạng lại số tiền (loại bỏ dấu , và .)
    price_columns = [
        # Đã loại bỏ các cột cần giữ nguyên định dạng từ danh sách này
    ]
    
    # Danh sách các cột cần định dạng lại số tiền và chuyển đổi "k" thành 000
    amount_columns_with_k_conversion = [
        # Đã loại bỏ các cột cần giữ nguyên định dạng từ danh sách này
    ]
    
    # Kết hợp tất cả các cột liên quan đến tiền tệ (đã loại trừ các cột cần giữ nguyên)
    all_currency_columns = price_columns + amount_columns_with_k_conversion
    
    # Các ký tự tiền tệ cần xóa
    currency_symbols = [" ₫", "₫", "đ", " đ"]
    
    # Theo yêu cầu mới: các cột cần xử lý đặc biệt
    multi_line_columns = ["Tên sản phẩm", "Mã phân loại"]
    numeric_id_columns = ["Mã shop (Shop ID)", "Mã sản phẩm"]

    # Dictionary lưu các vấn đề phát hiện được
    validation_issues = {
        "non_numeric_ids": [],         # Các trường hợp ID có chữ
        "multi_line_ids": [],          # Các trường hợp ID có nhiều dòng
        "sequential_ids": [],          # Các trường hợp ID liên tiếp tăng dần
        "numeric_product_names": [],   # Các trường hợp Tên sản phẩm chỉ chứa số
        "other_issues": []             # Các vấn đề khác
    }
    # Dictionary lưu trữ giá trị ID đã xử lý để phát hiện chuỗi liên tiếp dần
    shop_ids = []
    product_ids = []
    
    # Danh sách các cột phần trăm
    percent_columns = [
        "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
        "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
    ]
    
    # Tạo thêm danh sách lưu các thay đổi đã thực hiện để hiển thị trên UI
    changes_log = []
    
    # Xử lý từng bản ghi
    processed_data = []
    for idx, record in enumerate(data):
        # Tạo bản sao để tránh thay đổi dữ liệu gốc
        processed_record = record.copy()
        row_index = idx + 4  # Dòng thực tế trong sheet (bắt đầu từ 4)
        row_changes = []  # Lưu các thay đổi cho dòng này

        # 1. Xử lý các trường có nhiều dòng (Tên sản phẩm, Mã phân loại)
        for column in multi_line_columns:
            if column in processed_record and processed_record[column]:
                value = str(processed_record[column])
                # Xử lý xuống dòng (loại bỏ dòng trống)
                lines = value.split('\n')
                non_empty_lines = [line.strip() for line in lines if line.strip()]
                if len(non_empty_lines) != len(lines):
                    # Có dòng trống đã được loại bỏ
                    processed_value = '\n'.join(non_empty_lines)
                    logger.debug(f"Cleaned multi-line field in '{column}' at row {row_index}: removed {len(lines) - len(non_empty_lines)} empty lines")
                    processed_record[column] = processed_value

        # 2. Xử lý các trường ID (Mã shop, Mã sản phẩm)
        for column in numeric_id_columns:
            if column in processed_record and processed_record[column]:
                value = str(processed_record[column])
                original_value = value
                # Loại bỏ dấu nháy đơn ở đầu chuỗi (nếu có)
                if value.startswith("'"):
                    value = value[1:]
                    logger.debug(f"Removed single quote prefix from {column} at row {row_index}")
                # Loại bỏ khoảng trắng đầu và cuối
                value = value.strip()
                # Kiểm tra xuống dòng trong ID
                if '\n' in value:
                    lines = value.split('\n')
                    non_empty_lines = [line.strip() for line in lines if line.strip()]
                    if len(non_empty_lines) > 1:
                        # Ghi nhận lỗi: ID có nhiều dòng có dữ liệu, dùng dòng đó
                        validation_issues["multi_line_ids"].append({
                            "row": row_index,
                            "column": column,
                            "value": original_value,
                            "issue": "ID field contains multiple non-empty lines"
                        })
                        # Không thay đổi giá trị, để người dùng tự sửa
                    else:
                        # Chỉ có 1 dòng có dữ liệu, dùng dòng đó
                        value = non_empty_lines[0]
                # Nếu giá trị đã thay đổi, cập nhật lại record
                if value != original_value:
                    processed_record[column] = value
                
                # Kiểm tra ID có phải là số không
                if not value.isdigit():
                    # Ghi nhận lỗi: ID không phải là số
                    validation_issues["non_numeric_ids"].append({
                        "row": row_index,
                        "column": column,
                        "value": value,
                        "issue": "ID field contains non-numeric characters"
                    })
                # Thu thập ID để kiểm tra chuỗi liên tiếp
                if column == "Mã shop (Shop ID)" and value.isdigit():
                    shop_ids.append((row_index, int(value)))
                elif column == "Mã sản phẩm" and value.isdigit():
                    product_ids.append((row_index, int(value)))

        # 3. Xử lý các cột cần xóa nếu giá trị là "x", "X", "không", v.v.
        for column in columns_to_clean:
            if column in processed_record:
                value = str(processed_record[column]).strip() if processed_record[column] else ""
                # Kiểm tra nếu giá trị là một trong các giá trị cần xóa
                if value in values_to_remove:
                    logger.debug(f"Clearing column '{column}' with value '{value}'")
                    processed_record[column] = ""
                # Kiểm tra nếu giá trị chỉ có 1 ký tự là "x" hoặc "X"
                elif len(value) == 1 and value in ["x", "X", "/", "-"]:
                    logger.debug(f"Clearing column '{column}' with single character '{value}'")
                    processed_record[column] = ""

        # 4. Xử lý các cột giá tiền và số tiền 
        if not preserve_number_format:
            # Nếu không giữ nguyên định dạng số, thực hiện xử lý số tiền
            for column in all_currency_columns:
                if column in processed_record and processed_record[column]:
                    value = str(processed_record[column])
                    original_value = value
                    # Loại bỏ các ký tự tiền tệ
                    for symbol in currency_symbols:
                        if symbol in value:
                            value = value.replace(symbol, "")
                            logger.debug(f"Removed currency symbol '{symbol}' in column '{column}' value: '{original_value}'")
                    
                    # Xử lý đặc biệt cho các cột giá có thể chứa "k"
                    if column in amount_columns_with_k_conversion and "k" in value.lower():
                        value = value.lower().replace("k", "000")
                    
                    # Chỉ cập nhật nếu giá trị đã thay đổi
                    if value != original_value:
                        logger.debug(f"Reformatted value in column '{column}' from '{original_value}' to '{value}'")
                        processed_record[column] = value
        else:
            # Nếu giữ nguyên định dạng số, chỉ loại bỏ các ký tự tiền tệ
            for column in all_currency_columns:
                if column in processed_record and processed_record[column]:
                    value = str(processed_record[column])
                    original_value = value
                    # Chỉ loại bỏ các ký tự tiền tệ
                    for symbol in currency_symbols:
                        if symbol in value:
                            value = value.replace(symbol, "")
                            logger.debug(f"Removed currency symbol '{symbol}' in column '{column}' value: '{original_value}'")
                    
                    # Chỉ cập nhật nếu giá trị đã thay đổi
                    if value != original_value:
                        logger.debug(f"Removed currency symbols only in column '{column}' from '{original_value}' to '{value}'")
                        processed_record[column] = value

        # Đảm bảo cột cần giữ nguyên không bị sửa đổi
        for column in preserve_format_columns:
            if column in record and column in processed_record:
                # Khôi phục giá trị gốc từ record nếu có sự thay đổi
                if record[column] != processed_record[column]:
                    processed_record[column] = record[column]
                    logger.info(f"Preserved original format in column '{column}': '{record[column]}'")

        # Kiểm tra Tên sản phẩm - không được chỉ chứa toàn số
        if "Tên sản phẩm" in processed_record and processed_record["Tên sản phẩm"]:
            product_name = str(processed_record["Tên sản phẩm"]).strip()
            
            # Kiểm tra nếu chỉ chứa các chữ số
            if product_name.isdigit():
                validation_issues["numeric_product_names"].append({
                    "row": row_index,
                    "value": product_name,
                    "issue": "Product name contains only numeric characters"
                })
            # Kiểm tra nếu chỉ chứa các chữ số và ký tự đặc biệt
            elif all(c.isdigit() or c in [',', '.', '-', ' '] for c in product_name) and any(c.isdigit() for c in product_name):
                # Kiểm tra xem có ít nhất một ký tự chữ cái không
                has_letter = any(c.isalpha() for c in product_name)
                if not has_letter:
                    validation_issues["numeric_product_names"].append({
                        "row": row_index,
                        "value": product_name,
                        "issue": "Product name contains only numeric characters and punctuation"
                    })

        # Xử lý các cột phần trăm - CỐT LỐI XỬ LÝ PHẦN TRĂM CHỈ ĐỊNH DẠNG KHÔNG THAY ĐỔI GIÁ TRỊ
        for column in percent_columns:
            if column in processed_record and processed_record[column]:
                value = str(processed_record[column])
                if value:
                    # QUAN TRỌNG: GIỮ NGUYÊN GIÁ TRỊ PHẦN TRĂM, KHÔNG CHUYỂN THÀNH SỐ THẬP PHÂN
                    # Chỉ đảm bảo có ký tự % cuối cùng nếu chưa có
                    if not value.strip().endswith('%'):
                        processed_record[column] = value + '%'
                    else:
                        # Đã có dấu % rồi, giữ nguyên
                        processed_record[column] = value

        # Ghi log các thay đổi rõ ràng cho từng ô dữ liệu
        for column, new_value in processed_record.items():
            original_value = record.get(column)
            if new_value != original_value:
                row_changes.append({
                    "column": column,
                    "from": original_value,
                    "to": new_value,
                    "cell": f"{column}[{row_index}]"
                })
                logger.info(f"Fill down at cell {column}[{row_index}]: '{original_value}' -> '{new_value}'")
        
        if row_changes:
            changes_log.append({
                "row": row_index,
                "changes": row_changes
            })
            
        processed_data.append(processed_record)
    
    # Kiểm tra chuỗi ID liên tiếp
    # Cho Mã shop
    sequential_warnings = []  # Danh sách các cảnh báo về ID liên tiếp
    
    if len(shop_ids) > 2:
        shop_ids.sort(key=lambda x: x[1])  # Sắp xếp tăng dần theo giá trị ID
        sequences = []
        current_seq = [shop_ids[0]]
        for i in range(1, len(shop_ids)):
            if shop_ids[i][1] == shop_ids[i-1][1] + 1:  # Liên tiếp
                current_seq.append(shop_ids[i])
            else:
                if len(current_seq) >= 3:  # Chuỗi ít nhất 3 số liên tiếp
                    sequences.append(current_seq)
                current_seq = [shop_ids[i]]
        # Kiểm tra chuỗi cuối cùng
        if len(current_seq) >= 3:
            sequences.append(current_seq)
        
        # Đưa các chuỗi phát hiện được vào issues
        for seq in sequences:
            rows = [x[0] for x in seq]  # Lấy chỉ số dòng
            validation_issues["sequential_ids"].append({
                "column": "Mã shop (Shop ID)",
                "rows": rows,
                "values": [x[1] for x in seq],
                "issue": f"Found {len(seq)} sequential Shop IDs: {seq[0][1]} to {seq[-1][1]}"
            })
            
            # Thêm cảnh báo đặc biệt cho ID liên tiếp
            warning = f"⚠️ [CẢNH BÁO QUAN TRỌNG] Phát hiện {len(seq)} Mã shop liên tiếp từ dòng {min(rows)} đến dòng {max(rows)}: {seq[0][1]} → {seq[-1][1]}"
            sequential_warnings.append(warning)
            logger.warning(warning)
    
    # Tương tự cho Mã sản phẩm
    if len(product_ids) > 2:
        product_ids.sort(key=lambda x: x[1])
        sequences = []
        current_seq = [product_ids[0]]
        for i in range(1, len(product_ids)):
            if product_ids[i][1] == product_ids[i-1][1] + 1:
                current_seq.append(product_ids[i])
            else:
                if len(current_seq) >= 3:
                    sequences.append(current_seq)
                current_seq = [product_ids[i]]
        if len(current_seq) >= 3:
            sequences.append(current_seq)
        
        for seq in sequences:
            rows = [x[0] for x in seq]
            validation_issues["sequential_ids"].append({
                "column": "Mã sản phẩm",
                "rows": rows,
                "values": [x[1] for x in seq],
                "issue": f"Found {len(seq)} sequential Product IDs: {seq[0][1]} to {seq[-1][1]}"
            })
            
            # Thêm cảnh báo đặc biệt cho ID liên tiếp
            warning = f"⚠️ [CẢNH BÁO QUAN TRỌNG] Phát hiện {len(seq)} Mã sản phẩm liên tiếp từ dòng {min(rows)} đến dòng {max(rows)}: {seq[0][1]} → {seq[-1][1]}"
            sequential_warnings.append(warning)
            logger.warning(warning)
    
    # Bổ sung chi tiết thay đổi vào kết quả của validation_issues
    validation_issues["changes"] = changes_log
    
    logger.info(f"Data processing completed. {len(processed_data)} records processed.")
    logger.info(f"Validation issues found: {sum(len(issues) for issues in validation_issues.values())}")
    logger.info(f"  - Non-numeric IDs: {len(validation_issues['non_numeric_ids'])}")
    logger.info(f"  - Multi-line IDs: {len(validation_issues['multi_line_ids'])}")
    logger.info(f"  - Sequential IDs: {len(validation_issues['sequential_ids'])}")
    logger.info(f"  - Numeric product names: {len(validation_issues['numeric_product_names'])}")
    
    # Lưu danh sách cảnh báo ID liên tiếp để sử dụng bên ngoài
    process_copied_data.sequential_warnings = sequential_warnings
    
    return processed_data, validation_issues

# Update the batch_update_sheet function to use the Google API directly too
def batch_update_sheet(gs_service, spreadsheet_id: str, sheet_title: str, data: List[Dict[str, Any]], dest_headers: List[str], start_row: int = 4, validation_issues: Dict = None, preserve_number_format: bool = False, batch_callback = None) -> bool:
    """
    Cập nhật hàng loạt dữ liệu đã xử lý vào Google Sheet đích và áp dụng định dạng màu sắc cho các ô có vấn đề
    Args:
        gs_service: Google Sheets service object
        spreadsheet_id: ID của spreadsheet
        sheet_title: Tên sheet
        data: Dữ liệu đã xử lý
        dest_headers: Tiêu đề các cột đích
        start_row: Dòng bắt đầu (mặc định là 4)
        validation_issues: Các vấn đề xác thực được phát hiện
        preserve_number_format: Nếu True, giữ nguyên định dạng số (bao gồm dấu phẩy, dấu chấm)
        batch_callback: Callback để nhận thông tin về tiến trình xử lý
    Returns:
        True nếu thành công, False nếu thất bại
    """
    try:
        # Khởi tạo biến result để lưu kết quả xử lý
        result = {
            "processing_status": "pending",
            "message": f"Preparing to update sheet '{sheet_title}'",
            "total_rows": len(data)
        }
        
        # Thông báo bắt đầu xử lý qua callback
        if batch_callback:
            batch_callback({
                "message": f"Chuẩn bị xử lý {len(data)} dòng dữ liệu",
                "expected_rows": len(data)
            })
        
        logger.info(f"Preparing to batch update {len(data)} records to sheet '{sheet_title}'")
        logger.info(f"preserve_number_format = {preserve_number_format}")
        
        # Định nghĩa các danh sách cột
        # Danh sách cột yes/no
        yes_no_columns = ["Nhà bán hàng có đồng ý trả hoa hồng bổ sung cho Beyond K không?", 
                          "Nhà bán hàng đồng ý cho BEYONDK mượn sản phẩm chính + sản phẩm quà để KOL review trong Livestream?",
                          "Nhà bán hàng cho phép công bố giá sau cùng bán cho KOL trên livestream (giá sau khi đã trừ tất cả discount/voucher)?",
                          "Nhà bán hàng cho phép sử dụng hình ảnh/teaser video/teaser post về sản phẩm để truyền thông trước livestream ?"]
        
        # Danh sách các cột cần giữ nguyên định dạng theo yêu cầu
        preserve_format_columns = [
            "Giá gốc",
            "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
            "Mức giảm tối đa",
            "Áp dụng cho đơn từ"
        ]
        
        # Nếu preserve_number_format=True, thêm nhiều cột vào danh sách cần giữ nguyên định dạng
        if preserve_number_format:
            additional_preserve_columns = [
                "Giá bán dự kiến",
                "Giá bán còn lại sau khi trừ chi phí",
                "Giá sau cùng sau khi trừ tất cả discount",
                "Giá trị quà tặng kèm",
                "Số lượng tồn kho",
                "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
            ]
            # Thêm vào danh sách preserve nếu không trùng lặp
            for col in additional_preserve_columns:
                if col not in preserve_format_columns:
                    preserve_format_columns.append(col)
            
            logger.info(f"Using expanded list of columns to preserve format when preserve_number_format=True: {preserve_format_columns}")
        
        # Danh sách các cột có giá trị số cần giữ nguyên định dạng
        numeric_columns = [
            # Loại bỏ các cột cần giữ nguyên định dạng
            "Số lượng tồn kho", 
            "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)",
            "Giá trị quà tặng kèm"
        ]
        
        # Danh sách các cột ID
        id_columns = [
            "Mã shop (Shop ID)",
            "Mã sản phẩm",
            "Mã phân loại",
            "Mã quà tặng kèm (Item ID quà tặng) nếu có"
        ]
        
        # Danh sách các cột phần trăm
        percent_columns = [
            "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
            "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm",
            "% giảm giá"
        ]
        
        # Define yes/no terms to check
        yes_terms = ["đồng ý", "có", "yes", "y", "dong y", "ok",]
        no_terms = ["không đồng ý", "không", "no", "n", "khong dong y"]
        
        # Tham số tối ưu dựa trên external_update.py
        MAX_RETRIES = 3
        API_MIN_DELAY = 0.3
        last_api_call = 0

        # Hàm chờ giữa các lần gọi API
        def wait_between_api_calls():
            nonlocal last_api_call
            current_time = time.time()
            elapsed = current_time - last_api_call
            if elapsed < API_MIN_DELAY:
                wait_time = API_MIN_DELAY - elapsed
                time.sleep(wait_time)
            last_api_call = time.time()

        # Chia nhỏ dữ liệu thành các batch nhỏ hơn để tránh vượt quá giới hạn API
        MAX_ROWS_PER_BATCH = 50  # Giảm xuống còn 20 để đảm bảo độ tin cậy cao hơn
        batch_chunks = [data[i:i + MAX_ROWS_PER_BATCH] for i in range(0, len(data), MAX_ROWS_PER_BATCH)]

        # Khởi tạo thông tin sheet ID
        sheet_id = get_sheet_id_by_title(gs_service, spreadsheet_id, sheet_title)
        
        # Tham số tối ưu cho dữ liệu lớn
        MAX_RETRIES = 5  # Tăng số lần retry
        API_MIN_DELAY = 0.5  # Tăng thời gian delay giữa các lần gọi API
        RETRY_BACKOFF = 2  # Hệ số tăng thời gian chờ giữa các lần retry
        
        # Biến theo dõi tổng số dòng đã xử lý
        total_rows_updated = 0
        
        # Tìm các chỉ mục của cột cần giữ nguyên định dạng
        preserve_column_indices = []
        for col_name in preserve_format_columns:
            if col_name in dest_headers:
                col_idx = dest_headers.index(col_name)
                preserve_column_indices.append(col_idx)
                logger.info(f"Column to preserve format: {col_name} at index {col_idx}")
        
        # Nếu preserve_number_format=True, thêm tất cả các cột số vào danh sách cần giữ nguyên định dạng
        if preserve_number_format:
            # Thêm các cột số tiền
            for col_name in numeric_columns:
                if col_name in dest_headers and dest_headers.index(col_name) not in preserve_column_indices:
                    col_idx = dest_headers.index(col_name)
                    preserve_column_indices.append(col_idx)
                    logger.info(f"Preserving number format for column: {col_name} at index {col_idx}")
            
            # Thêm các cột về tiền tệ/giá trị khác
            additional_money_columns = [
                "Giá bán dự kiến",
                "Giá bán còn lại sau khi trừ chi phí",
                "Giá sau cùng sau khi trừ tất cả discount",
                "Giá trị quà tặng kèm"
            ]
            
            for col_name in additional_money_columns:
                if col_name in dest_headers and dest_headers.index(col_name) not in preserve_column_indices:
                    col_idx = dest_headers.index(col_name)
                    preserve_column_indices.append(col_idx)
                    logger.info(f"Preserving number format for additional money column: {col_name} at index {col_idx}")
        
        for chunk_idx, chunk in enumerate(batch_chunks):
            # Xây dựng dữ liệu cho updateCells request
            rows = []
            current_start_row = start_row + chunk_idx * MAX_ROWS_PER_BATCH
            
            for attempt in range(MAX_RETRIES):
                try:
                    # Chuẩn bị dữ liệu theo định dạng 2D array cho API values.update
                    values_array = []
                    
                    for record in chunk:
                        row_values = []
                        for header in dest_headers:
                            value = record.get(header, "")
                            row_values.append(value)
                        values_array.append(row_values)
                    
                    # Tính cột cuối cùng của dữ liệu
                    max_cols = len(dest_headers)
                    last_col = chr(65 + max_cols - 1) if max_cols <= 26 else chr(64 + max_cols // 26) + chr(65 + max_cols % 26)
                    update_range = f"'{sheet_title}'!A{current_start_row}:{last_col}{current_start_row + len(chunk) - 1}"
                    
                    # Trước tiên, cập nhật toàn bộ dữ liệu bằng RAW để tránh Google Sheets tự động định dạng
                    body = {
                        'values': values_array
                    }
                    wait_between_api_calls()
                    gs_service.spreadsheets().values().update(
                        spreadsheetId=spreadsheet_id,
                        range=update_range,
                        valueInputOption='RAW',  # Sử dụng RAW thay vì USER_ENTERED
                        body=body
                    ).execute()
                    
                    # Sau đó, cập nhật từng cột cần giữ nguyên định dạng riêng lẻ
                    for col_idx in preserve_column_indices:
                        col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx // 26) + chr(65 + col_idx % 26)
                        # Trích xuất giá trị cho cột này
                        col_values = []
                        for record in chunk:
                            col_name = dest_headers[col_idx]
                            value = record.get(col_name, "")
                            col_values.append([value])  # API values.update cần format [[value1], [value2], ...]
                        
                        # Cập nhật riêng cột này với USER_ENTERED để hiểu định dạng
                        col_body = {
                            'values': col_values
                        }
                        preserve_range = f"'{sheet_title}'!{col_letter}{current_start_row}:{col_letter}{current_start_row + len(chunk) - 1}"
                        
                        wait_between_api_calls()
                        gs_service.spreadsheets().values().update(
                            spreadsheetId=spreadsheet_id,
                            range=preserve_range,
                            valueInputOption='USER_ENTERED',  # Hiểu giá trị như người dùng nhập vào
                            body=col_body
                        ).execute()
                        
                        logger.info(f"Updated preserved column {col_letter} with USER_ENTERED mode")
                    
                    # Cập nhật tổng số dòng đã xử lý
                    total_rows_updated += len(chunk)
                    logger.info(f"Successfully updated batch {chunk_idx+1}/{len(batch_chunks)} ({len(chunk)} rows)")
                    
                    # Gọi callback nếu có để cập nhật tiến trình
                    if batch_callback:
                        batch_callback({
                            "batch_index": chunk_idx + 1,
                            "total_batches": len(batch_chunks),
                            "rows": len(chunk),
                            "total_rows_updated": total_rows_updated,
                            "expected_rows": len(data)
                        })
                    
                    # Thành công, thoát vòng lặp retry
                    break
                except Exception as e:
                    logger.error(f"Error updating batch {chunk_idx+1}: {str(e)}")
                    if attempt < MAX_RETRIES - 1:
                        # Tăng thời gian chờ giữa các lần thử lại
                        wait_time = (attempt + 1) * RETRY_BACKOFF  # 2, 4, 6 giây
                        logger.info(f"Waiting {wait_time} seconds before retry...")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"Failed to update batch {chunk_idx+1} after {MAX_RETRIES} attempts")
            
            # Đợi giữa các batch để tránh quá tải API
            time.sleep(1)
        
        # Nếu có các vấn đề xác thực, áp dụng định dạng màu sắc cho các ô có vấn đề
        if validation_issues:
            format_requests = []
        
        # Áp dụng định dạng cho các cột phần trăm và tiền tệ sau khi đã cập nhật xong dữ liệu
        if total_rows_updated > 0 and preserve_number_format:
            # Tìm chỉ mục của các cột phần trăm và tiền tệ
            percent_column_indices = []
            money_column_indices = []
            
            # Danh sách cột phần trăm
            percent_columns = [
                "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
                "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm",
                "% giảm giá"
            ]
            
            # Danh sách cột tiền tệ
            money_format_columns = [
                "Giá gốc",
                "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
                "Giá bán dự kiến",
                "Giá bán còn lại sau khi trừ chi phí",
                "Giá sau cùng sau khi trừ tất cả discount",
                "Giá trị quà tặng kèm",
                "Mức giảm tối đa",
                "Áp dụng cho đơn từ"
            ]
            
            # Tìm chỉ mục của các cột
            for col_name in percent_columns:
                if col_name in dest_headers:
                    col_idx = dest_headers.index(col_name)
                    percent_column_indices.append(col_idx)
                    logger.info(f"Found percent column '{col_name}' at index {col_idx}")
            
            for col_name in money_format_columns:
                if col_name in dest_headers:
                    col_idx = dest_headers.index(col_name)
                    money_column_indices.append(col_idx)
                    logger.info(f"Found money column '{col_name}' at index {col_idx}")
            
            # Áp dụng định dạng
            format_requests = []
            
            # 1. Áp dụng định dạng phần trăm
            for col_idx in percent_column_indices:
                format_requests.append({
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": start_row - 1,  # 0-based
                            "endRowIndex": start_row + total_rows_updated - 1,
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "numberFormat": {
                                    "type": "PERCENT",
                                    "pattern": "0.00%"  # Hiển thị 2 chữ số thập phân
                                }
                            }
                        },
                        "fields": "userEnteredFormat.numberFormat"
                    }
                })
                logger.info(f"Added percent formatting request for column index {col_idx}")
            
            # 2. Áp dụng định dạng tiền tệ
            for col_idx in money_column_indices:
                format_requests.append({
                    "repeatCell": {
                        "range": {
                            "sheetId": sheet_id,
                            "startRowIndex": start_row - 1,  # 0-based
                            "endRowIndex": start_row + total_rows_updated - 1,
                            "startColumnIndex": col_idx,
                            "endColumnIndex": col_idx + 1
                        },
                        "cell": {
                            "userEnteredFormat": {
                                "numberFormat": {
                                    "type": "NUMBER",
                                    "pattern": "#,##0"  # Định dạng có dấu phẩy ngăn cách hàng nghìn
                                }
                            }
                        },
                        "fields": "userEnteredFormat.numberFormat"
                    }
                })
                logger.info(f"Added money formatting request for column index {col_idx}")
            
            # Thực hiện các yêu cầu định dạng
            if format_requests:
                try:
                    format_body = {"requests": format_requests}
                    gs_service.spreadsheets().batchUpdate(
                        spreadsheetId=spreadsheet_id,
                        body=format_body
                    ).execute()
                    logger.info(f"Applied special formatting to {len(format_requests)} columns")
                    
                    # Cập nhật conditional formatting cho Yes/No columns
                    try:
                        # Lấy sheet_id dựa trên sheet_title
                        sheet_id = get_sheet_id_by_title(gs_service, spreadsheet_id, sheet_title)
                        if sheet_id:
                            # Thiết lập lại conditional formatting cho Yes/No
                            setup_data_validation_for_yes_no_columns(gs_service, spreadsheet_id, sheet_id, dest_headers)
                            logger.info(f"Re-applied conditional formatting for Yes/No columns")
                    except Exception as e:
                        logger.error(f"Error applying Yes/No conditional formatting: {str(e)}")
                    
                    # Thêm phương pháp đặc biệt cho các cột phần trăm quan trọng
                    for col_idx in percent_column_indices:
                        try:
                            col_name = dest_headers[col_idx]
                            col_letter = chr(65 + col_idx) if col_idx < 26 else chr(64 + col_idx // 26) + chr(65 + col_idx % 26)
                            
                            # Lấy giá trị hiện tại
                            range_to_read = f"'{sheet_title}'!{col_letter}{start_row}:{col_letter}{start_row + total_rows_updated - 1}"
                            result = gs_service.spreadsheets().values().get(
                                spreadsheetId=spreadsheet_id,
                                range=range_to_read,
                                valueRenderOption='UNFORMATTED_VALUE'  # Lấy giá trị gốc
                            ).execute()
                            
                            values = result.get('values', [])
                            if values:
                                # Xử lý giá trị để đảm bảo có dạng phần trăm
                                formatted_values = []
                                for row in values:
                                    if row and row[0]:
                                        value = row[0]
                                        # Nếu là số, chuyển đổi sang chuỗi dạng phần trăm
                                        if isinstance(value, (int, float)):
                                            # Đảm bảo giá trị nằm trong khoảng 0-1
                                            if value > 1 and value <= 100:
                                                # KHÔNG chuyển đổi phần trăm thành số thập phân
                                                # value = value / 100  # Chuyển từ 20% -> 0.2
                                                formatted_values.append([f"{int(value)}%"])
                                            else:
                                                # Nếu đã là số thập phân nhỏ, giả định là phần trăm đã được chia
                                                if 0 <= value <= 1:
                                                    # Chuyển về dạng phần trăm nguyên
                                                    formatted_values.append([f"{int(value * 100)}%"])
                                                else:
                                                    formatted_values.append([f"{value}%"])
                                        else:
                                            # Cố gắng chuyển đổi chuỗi thành số rồi định dạng
                                            try:
                                                num_value = float(str(value).replace("%", "").strip())
                                                if num_value > 1 and num_value <= 100:
                                                    # KHÔNG chuyển đổi phần trăm thành số thập phân
                                                    # num_value = num_value / 100
                                                    formatted_values.append([f"{int(num_value)}%"])
                                            except:
                                                formatted_values.append([value])
                                    else:
                                        formatted_values.append([""])
                                
                                # Cập nhật lại với định dạng phần trăm
                                body = {
                                    'values': formatted_values
                                }
                                
                                # Sử dụng USER_ENTERED để Google Sheets hiểu đây là phần trăm
                                gs_service.spreadsheets().values().update(
                                    spreadsheetId=spreadsheet_id,
                                    range=range_to_read,
                                    valueInputOption='USER_ENTERED',
                                    body=body
                                ).execute()
                                
                                logger.info(f"Applied special percentage handling for column {col_name}")
                        except Exception as e:
                            logger.error(f"Error in special handling for column {col_name}: {str(e)}")
                except Exception as e:
                    logger.error(f"Error applying special formatting: {str(e)}")
        
        result["processing_status"] = "success"
        result["message"] = f"Successfully updated {total_rows_updated} rows to sheet '{sheet_title}'"
        logger.info(result["message"])
        return True
    except Exception as e:
        logger.error(f"Error updating sheet '{sheet_title}': {str(e)}")
        result["processing_status"] = "error"
        result["error"] = (f"Error during data processing: {str(e)}")
        logger.error(f"Error during data processing: {str(e)}")
        return False

# Update the get_gspread_client function to use OAuth credentials
def get_gspread_client(gs_service):
    """
    Create a gspread client using an existing authorized Google API service
    Args:
        gs_service: An authorized Google Sheets API service object
    Returns:
        A gspread client that reuses the existing credentials
    """
    try:
        # Create a gspread client that uses the existing oauth2client Credentials
        # This avoids the need to create separate service account credentials
        from oauth2client.client import GoogleCredentials
        import gspread
        from gspread.client import Client
        
        # Create a custom gspread Client that reuses the existing HTTP object
        client = Client(auth=gs_service._http)
        logger.info("Successfully created gspread client using OAuth credentials")
        return client
    except Exception as e:
        logger.error(f"Error creating gspread client: {e}")
        return None

def get_sheet_id_by_title(gs_service, spreadsheet_id: str, sheet_title: str) -> int:
    """
    Lấy sheet_id dựa trên tên sheet
    Args:
        gs_service: Google Sheets service object
        spreadsheet_id: ID của spreadsheet
        sheet_title: Tên sheet cần tìm
    Returns:
        sheet_id nếu tìm thấy, None nếu không tìm thấy
    """
    try:
        spreadsheet = gs_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        sheets = spreadsheet.get('sheets', [])
        for sheet in sheets:
            if sheet['properties']['title'] == sheet_title:
                return sheet['properties']['sheetId']
        logger.warning(f"Sheet '{sheet_title}' not found in spreadsheet {spreadsheet_id}")
        return None
    except Exception as e:
        logger.error(f"Error getting sheet ID for '{sheet_title}': {str(e)}")
        return None

def run_core_logic(
    source_input: str,
    dest_input: str,
    selected_source_sheets: List[str],
    credentials_data: str,
    custom_mapping: Dict[str, Dict[str, str]] = None,
    start_rows: Dict[str, int] = None,
    user_choice: str = "clear_data",
    preserve_number_format: bool = True,
    batch_callback = None  # Thêm callback để theo dõi tiến trình
) -> Dict[str, Any]:
    """
    Xử lý logic chính của ứng dụng, copy dữ liệu từ các sheet nguồn sang sheet đích
    
    Args:
        source_input: ID hoặc URL của spreadsheet nguồn
        dest_input: ID hoặc URL của spreadsheet đích
        selected_source_sheets: Danh sách các sheet nguồn đã chọn
        credentials_data: Dữ liệu xác thực cho Google Sheets API
        custom_mapping: Ánh xạ tùy chỉnh giữa các cột (tùy chọn)
        start_rows: Dòng bắt đầu đọc dữ liệu cho mỗi sheet (tùy chọn)
        user_choice: Lựa chọn 'clear_data' hoặc 'append_to_next_row'
        preserve_number_format: Nếu True, giữ nguyên định dạng số từ sheet nguồn (bao gồm dấu phẩy, dấu chấm)
        batch_callback: Callback để cập nhật tiến trình xử lý batch
    
    Returns:
        Dict chứa kết quả xử lý và thống kê
    """
    # Khởi tạo kết quả
    result = {
        "status": "success",
        "total_rows": 0,
        "sheets_processed": 0,
        "errors": []
    }
    
    try:
        # Chuẩn bị ID spreadsheet
        source_id = extract_spreadsheet_id(source_input)
        dest_id = extract_spreadsheet_id(dest_input)
        
        # Khởi tạo Google Sheet Manager
        gs_manager = GoogleSheetManager(auth_type='oauth', credentials_data=credentials_data)
        gs_service = gs_manager.get_sheets_service()
        
        # Lấy thông tin các sheet nguồn
        source_spreadsheet = gs_service.spreadsheets().get(spreadsheetId=source_id).execute()
        source_sheets = source_spreadsheet.get("sheets", [])
        
        # Lọc chỉ các sheet đã chọn
        selected_sheets = []
        for sheet in source_sheets:
            sheet_title = sheet["properties"]["title"]
            if sheet_title in selected_source_sheets:
                selected_sheets.append(sheet)
        
        # Lấy thông tin sheet đích
        dest_sheet_id = None
        dest_sheet_title = "Pool Deal"  # Mặc định là "Pool Deal"
        
        # Kiểm tra sheet đích trong spreadsheet đích
        dest_spreadsheet = gs_service.spreadsheets().get(spreadsheetId=dest_id).execute()
        dest_sheets = dest_spreadsheet.get("sheets", [])
        
        for sheet in dest_sheets:
            if sheet["properties"]["title"] == dest_sheet_title:
                dest_sheet_id = sheet["properties"]["sheetId"]
                break
        
        # Nếu không tìm thấy sheet đích, tạo mới từ template
        if not dest_sheet_id:
            dest_sheet_id, dest_sheet_title = copy_sheet_template(
                gs_service, 
                TEMPLATE_SPREADSHEET_ID, 
                0,  # sẽ lấy sheet id thực tế từ hàm
                dest_id, 
                dest_sheet_title
            )
            if not dest_sheet_id:
                result["status"] = "error"
                result["error"] = "Failed to create destination sheet"
                return result
        
        # Lấy headers từ sheet đích
        dest_headers = get_headers_from_sheet(gs_service, dest_id, dest_sheet_title)
        if not dest_headers:
            result["status"] = "error"
            result["error"] = "Failed to get headers from destination sheet"
            return result
        
        # Cache cho việc ánh xạ headers
        header_cache = {}
        
        # Xử lý từng sheet nguồn
        processed_records = []
        total_records = 0
        
        for sheet in selected_sheets:
            sheet_title = sheet["properties"]["title"]
            sheet_id = sheet["properties"]["sheetId"]
            
            # Xác định dòng bắt đầu
            start_row = None
            if start_rows and sheet_title in start_rows:
                start_row = start_rows[sheet_title]
            
            # Lấy mapping cho sheet hiện tại
            mapping = None
            if custom_mapping and sheet_title in custom_mapping:
                mapping = custom_mapping[sheet_title]
            else:
                # Tạo mapping tự động
                source_headers = get_headers_from_sheet(gs_service, source_id, sheet_title)
                if source_headers:
                    mapping = auto_map_columns(source_headers, dest_headers)
            
            if not mapping:
                logger.warning(f"No mapping found for sheet {sheet_title}")
                result["errors"].append(f"No mapping found for sheet {sheet_title}")
                continue
            
            # Xử lý dữ liệu sheet
            records = process_sheet_data(gs_service, source_id, sheet, mapping, header_cache, start_row, preserve_number_format)
            
            if records:
                logger.info(f"Processed {len(records)} records from sheet {sheet_title}")
                processed_records.extend(records)
                total_records += len(records)
            else:
                logger.warning(f"No records processed from sheet {sheet_title}")
        
        # Kiểm tra nếu không có dữ liệu để copy
        if not processed_records:
            result["status"] = "no_data_to_copy"
            return result
        
        # Ghi dữ liệu vào sheet đích mà không xử lý thêm
        # (Loại bỏ việc gọi process_copied_data)
        batch_success = batch_update_sheet(
            gs_service, 
            dest_id, 
            dest_sheet_title, 
            processed_records,  # Sử dụng trực tiếp processed_records thay vì processed_data
            dest_headers,
            4,  # Luôn bắt đầu từ dòng 4
            None,  # Không còn validation_issues
            preserve_number_format,  # Truyền tham số preserve_number_format
            batch_callback  # Truyền callback để cập nhật tiến trình
        )
        
        if not batch_success:
            result["status"] = "error"
            result["error"] = "Failed to update destination sheet"
            return result
        
        # Cập nhật kết quả
        result["total_rows"] = total_records
        result["sheets_processed"] = len(selected_sheets)
        
        # Lưu thông tin shop ID và product code để sử dụng sau này
        shop_ids = []
        product_codes = []
        
        for record in processed_records:  # Sử dụng processed_records thay vì processed_data
            if "Mã shop (Shop ID)" in record and record["Mã shop (Shop ID)"]:
                shop_ids.append(str(record["Mã shop (Shop ID)"]))
            if "Mã sản phẩm" in record and record["Mã sản phẩm"]:
                product_codes.append(str(record["Mã sản phẩm"]))
        
        if shop_ids:
            save_temp_shop_ids(shop_ids)
        
        if product_codes:
            save_temp_product_codes(product_codes)
        
        # Lưu thông tin destination để sử dụng sau này
        save_temp_dest_data(dest_id, dest_sheet_title)
        
        return result
    
    except Exception as e:
        logger.error(f"Error in run_core_logic: {str(e)}")
        result["status"] = "error"
        result["error"] = str(e)
        return result

def read_data_from_sheet(
    gs_service, 
    spreadsheet_id: str, 
    sheet_id: int, 
    sheet_title: str,
    range_name: str = None,
    start_column: str = None,
    end_column: str = None,
    start_row: int = None,
    use_header_row: bool = True,
    process_percent_values: bool = True
) -> Tuple[List[str], List[Dict[str, Any]]]:
    try:
        value_range_params = {
            "spreadsheetId": spreadsheet_id,
            "majorDimension": "ROWS",
            # QUAN TRỌNG: Thay đổi từ UNFORMATTED_VALUE sang FORMATTED_VALUE
            # để lấy các giá trị đã được định dạng (bao gồm cả định dạng phần trăm)
            "valueRenderOption": "FORMATTED_VALUE"
        }
        
        # Ưu tiên dùng range_name nếu được cung cấp
        if range_name:
            value_range_params["range"] = range_name
        else:
            # Nếu không có range_name, tạo range từ sheet title và các tham số khác
            if sheet_title and start_column and end_column and start_row:
                value_range_params["range"] = f"'{sheet_title}'!{start_column}{start_row}:{end_column}"
            else:
                # Nếu không đủ tham số, dùng toàn bộ sheet
                value_range_params["range"] = f"'{sheet_title}'"
        
        # Đọc dữ liệu từ sheet
        result = gs_service.spreadsheets().values().get(**value_range_params).execute()
        values = result.get("values", [])
        
        if not values:
            logger.warning(f"No data found in sheet '{sheet_title}'")
            return [], []
        
        # Lấy header từ dòng đầu tiên
        if use_header_row:
            headers = values[0]
            data_rows = values[1:]
        else:
            # Nếu không dùng header, tạo header mặc định A, B, C, ...
            num_cols = max(len(row) for row in values)
            headers = [chr(65 + i) for i in range(num_cols)]  # A, B, C, ...
            data_rows = values
        
        # Chuyển đổi dữ liệu thành danh sách các dictionary
        result_data = []
        
        for row in data_rows:
            # Ensure row has enough elements to match headers
            if len(row) < len(headers):
                row.extend([""] * (len(headers) - len(row)))
            
            # Xử lý các giá trị trong dòng
            row_data = {}
            for idx, header in enumerate(headers):
                if idx < len(row):
                    value = row[idx]
                    
                    # Xử lý các trường hợp đặc biệt
                    if isinstance(value, str):
                        # 1. Xử lý các giá trị phần trăm
                        if process_percent_values and "%" in value:
                            # GIỮ NGUYÊN giá trị phần trăm, không chuyển thành số thập phân
                            # Ví dụ "31%" sẽ được giữ là "31%", không phải 0.31
                            row_data[header] = value.strip()
                        # 2. Xử lý các giá trị YES/NO
                        elif value.upper() in ["ĐỒNG Ý", "KHÔNG ĐỒNG Ý", "YES", "NO", "Y", "N"]:
                            row_data[header] = normalize_yes_no_value(value)
                        # 3. Xử lý các giá trị thông thường
                        else:
                            # Đối với giá trị tiền tệ, giữ nguyên cả dấu phẩy phân cách hàng nghìn
                            row_data[header] = value
                    else:
                        row_data[header] = value
                else:
                    row_data[header] = ""
            
            result_data.append(row_data)
        
        return headers, result_data
    except Exception as e:
        logger.error(f"Error reading data from sheet '{sheet_title}': {e}")
        return [], []

def normalize_yes_no_value(value):
    """
    Chuẩn hóa giá trị có kiểu yes/no thành giá trị "ĐỒNG Ý" hoặc "KHÔNG ĐỒNG Ý".
    """
    if not value:
        return ""
    
    value_lower = str(value).lower().strip()
    
    # Các từ khóa cho "yes"
    yes_terms = ["đồng ý", "có", "yes", "y", "dong y", "ok", "+", "đồng y", "được"]
    
    # Các từ khóa cho "no"
    no_terms = ["không đồng ý", "không", "no", "n", "khong dong y", "ko", "-", "khong", "k"]
    
    # Kiểm tra từng từ khóa
    for term in yes_terms:
        if term in value_lower:
            return "ĐỒNG Ý"
    
    for term in no_terms:
        if term in value_lower:
            return "KHÔNG ĐỒNG Ý"
    
    # Nếu không nhận dạng được, giữ nguyên giá trị
    return value

def convert_percent_value(value):
    """
    Chuyển đổi giá trị phần trăm thành định dạng chuẩn mà không làm mất dữ liệu
    """
    if not value:
        return ""
    
    # Nếu đã là số thì giữ nguyên và thêm % nếu cần
    if isinstance(value, (int, float)):
        # Kiểm tra xem đây có phải là giá trị phần trăm nhỏ (0.xx) hay không
        if 0 <= value <= 1:
            # Đây có thể là phần trăm đã chia cho 100 (0.31 cho 31%)
            return f"{int(value * 100)}%"
        else:
            # Giá trị lớn hơn 1, giữ nguyên (31 cho 31%)
            return f"{int(value)}%"
    
    # Nếu là chuỗi, xử lý chuỗi
    if isinstance(value, str):
        # Loại bỏ khoảng trắng và ký tự %
        cleaned_value = value.strip()
        
        # Nếu đã có % ở cuối, giữ nguyên
        if cleaned_value.endswith("%"):
            return cleaned_value
        
        try:
            # Chuyển thành số
            num_value = float(cleaned_value)
            
            # Áp dụng cùng logic như với giá trị số
            if 0 <= num_value <= 1:
                return f"{int(num_value * 100)}%"
            else:
                return f"{int(num_value)}%"
        except ValueError:
            # Không thể chuyển thành số, giữ nguyên
            return value
    
    # Trường hợp khác, giữ nguyên
    return value

def handle_special_format_columns(value, column_type=None):
    """
    Hàm mới xử lý các định dạng đặc biệt cho các loại cột cụ thể
    
    Args:
        value: Giá trị cần xử lý
        column_type: Loại cột (percent, yes_no, money, etc.)
    
    Returns:
        Giá trị đã xử lý với định dạng phù hợp
    """
    if not value:
        return ""
    
    if column_type == "percent":
        # Xử lý giá trị phần trăm - luôn giữ dạng số nguyên + %
        if isinstance(value, (int, float)):
            # Nếu giá trị là số thập phân nhỏ (0.31), chuyển thành 31%
            if 0 <= value <= 1:
                return f"{int(value * 100)}%"
            else:
                # Nếu giá trị đã là số nguyên (31), thêm % vào cuối
                return f"{int(value)}%"
        elif isinstance(value, str):
            # Xử lý giá trị dạng chuỗi
            cleaned_value = value.strip().replace("phần trăm", "")
            
            # Nếu đã có % ở cuối, kiểm tra xem có phải số thập phân không
            if cleaned_value.endswith("%"):
                cleaned_value = cleaned_value[:-1].strip()
                try:
                    num_value = float(cleaned_value)
                    if 0 < num_value < 1:  # Đây là số thập phân nhỏ (0.xx)
                        return f"{int(num_value * 100)}%"
                    return f"{int(num_value)}%"  # Làm tròn và thêm %
                except ValueError:
                    # Không phải số, giữ nguyên với % ở cuối
                    return value
            
            # Không có % ở cuối, thử chuyển thành số
            try:
                num_value = float(cleaned_value)
                if 0 < num_value < 1:  # Đây là số thập phân nhỏ (0.xx)
                    return f"{int(num_value * 100)}%"
                return f"{int(num_value)}%"  # Làm tròn và thêm %
            except ValueError:
                # Không phải số, thêm % vào cuối
                if not value.endswith("%"):
                    return f"{value}%"
                return value
    
    elif column_type == "yes_no":
        # Xử lý giá trị Yes/No - luôn chuẩn hóa thành ĐỒNG Ý hoặc KHÔNG ĐỒNG Ý
        if isinstance(value, str):
            value_lower = value.lower().strip()
            
            # Các từ khóa cho "yes"
            yes_terms = ["đồng ý", "có", "yes", "y", "dong y", "ok", "+", "đồng y", "được", "duoc"]
            
            # Các từ khóa cho "no"
            no_terms = ["không đồng ý", "không", "no", "n", "khong dong y", "ko", "-", "khong"]
            
            # Kiểm tra từng từ khóa
            for term in yes_terms:
                if term in value_lower:
                    return "ĐỒNG Ý"
            
            for term in no_terms:
                if term in value_lower:
                    return "KHÔNG ĐỒNG Ý"
        
        # Nếu không nhận dạng được, giữ nguyên giá trị
        return value
    
    # Mặc định giữ nguyên giá trị nếu không có xử lý đặc biệt
    return value
